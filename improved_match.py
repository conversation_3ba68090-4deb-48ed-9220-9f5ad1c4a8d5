import pandas as pd
import re
import unicodedata
from difflib import <PERSON><PERSON><PERSON><PERSON><PERSON>


def improved_match(file_a, file_b, result_file,
                  sheet_a='Sheet1', sheet_b='Sheet1',
                  header_row=1):
    """
    한국어 제품명 매칭에 특화된 개선된 매칭 함수
    """
    print("📂 파일 읽기 중...")
    
    # 파일 읽기 (강화된 인코딩 처리)
    def read_excel_robust(file_path, sheet_name, header_row):
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row, engine='openpyxl')
            print(f"  - {file_path}: 성공적으로 읽기 완료")
            return df
        except Exception as e:
            print(f"  - {file_path}: 읽기 실패 - {str(e)}")
            raise
    
    df_a = read_excel_robust(file_a, sheet_a, header_row)
    df_b = read_excel_robust(file_b, sheet_b, header_row)
    
    print(f"  - {file_a}: {len(df_a):,}행")
    print(f"  - {file_b}: {len(df_b):,}행")

    # 컬럼 찾기
    def find_columns(df, file_name):
        print(f"\n📋 {file_name} 컬럼 목록:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. '{col}'")
        
        # 구성품 컬럼 찾기
        component_col = None
        barcode_col = None
        
        for col in df.columns:
            col_str = str(col).strip().lower()
            if any(name in col_str for name in ['구성품', '제품명', '상품명', '품목']):
                component_col = col
            if any(name in col_str for name in ['bar_cd', 'barcode', '바코드']):
                barcode_col = col
        
        return component_col, barcode_col

    qaid_col_a, barcode_col_a = find_columns(df_a, "파일 A")
    qaid_col_b, barcode_col_b = find_columns(df_b, "파일 B")
    
    if not qaid_col_a or not qaid_col_b:
        raise Exception("구성품 컬럼을 찾을 수 없습니다.")
    
    print(f"\n🔍 사용할 컬럼:")
    print(f"  - A파일 구성품: '{qaid_col_a}'")
    print(f"  - B파일 구성품: '{qaid_col_b}'")
    print(f"  - A파일 bar_cd: '{barcode_col_a}'" if barcode_col_a else "  - A파일 bar_cd: 없음")
    print(f"  - B파일 bar_cd: '{barcode_col_b}'" if barcode_col_b else "  - B파일 bar_cd: 없음")

    # 고급 텍스트 정규화
    def advanced_normalize(text):
        """한국어 제품명에 특화된 정규화"""
        if pd.isna(text) or text is None:
            return ""
        
        text = str(text).strip()
        
        # Unicode 정규화
        text = unicodedata.normalize('NFC', text)
        
        # 다양한 공백 문자 통일
        text = re.sub(r'[\s\u00A0\u1680\u2000-\u200B\u202F\u205F\u3000\uFEFF]+', ' ', text)
        
        # 특수문자 정리 (괄호 내용 제거 옵션)
        # text = re.sub(r'\([^)]*\)', '', text)  # 괄호 내용 제거
        
        # 영문 대소문자 통일
        text = text.upper()
        
        # 숫자와 문자 사이 공백 정리
        text = re.sub(r'(\d)([가-힣A-Z])', r'\1 \2', text)
        text = re.sub(r'([가-힣A-Z])(\d)', r'\1 \2', text)
        
        # 연속 공백 제거
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text

    # 데이터 정규화
    print(f"\n🔧 데이터 정규화 중...")
    df_a[qaid_col_a] = df_a[qaid_col_a].apply(advanced_normalize)
    df_b[qaid_col_b] = df_b[qaid_col_b].apply(advanced_normalize)
    
    # 빈 값 제거
    df_a_clean = df_a[df_a[qaid_col_a] != ''].copy()
    df_b_clean = df_b[df_b[qaid_col_b] != ''].copy()
    
    print(f"  - A파일 유효 데이터: {len(df_a_clean):,}개")
    print(f"  - B파일 유효 데이터: {len(df_b_clean):,}개")

    # 샘플 출력
    print(f"\n📋 정규화된 A파일 샘플:")
    for i, value in enumerate(df_a_clean[qaid_col_a].head(10)):
        print(f"  {i+1}. '{value}'")
    
    print(f"\n📋 정규화된 B파일 샘플:")
    for i, value in enumerate(df_b_clean[qaid_col_b].head(10)):
        print(f"  {i+1}. '{value}'")

    # 고급 유사도 계산 함수
    def calculate_similarity(text1, text2):
        """다중 방식 유사도 계산"""
        if not text1 or not text2:
            return 0, "빈값"
        
        text1 = text1.strip()
        text2 = text2.strip()
        
        # 1. 정확 일치
        if text1 == text2:
            return 1.0, "정확일치"
        
        # 2. 포함 관계 (양방향)
        if text1 in text2:
            return 0.95, "A포함B"
        if text2 in text1:
            return 0.95, "B포함A"
        
        # 3. 문자열 유사도 (SequenceMatcher)
        seq_ratio = SequenceMatcher(None, text1, text2).ratio()
        if seq_ratio >= 0.8:
            return seq_ratio, "문자열유사도"
        
        # 4. 단어 기반 매칭
        words1 = set(re.findall(r'[가-힣A-Z0-9]+', text1))
        words2 = set(re.findall(r'[가-힣A-Z0-9]+', text2))
        
        if words1 and words2:
            # Jaccard 유사도
            intersection = len(words1 & words2)
            union = len(words1 | words2)
            jaccard = intersection / union if union > 0 else 0
            
            if jaccard >= 0.5:
                return jaccard, "단어매칭"
            
            # 부분 단어 매칭 (한 쪽의 모든 단어가 다른 쪽에 포함)
            if words1.issubset(words2) or words2.issubset(words1):
                return 0.7, "부분단어매칭"
        
        # 5. 키워드 기반 매칭 (제품 특성 고려)
        # 숫자+단위 패턴 매칭
        pattern1 = re.findall(r'\d+[가-힣A-Z]*', text1)
        pattern2 = re.findall(r'\d+[가-힣A-Z]*', text2)
        
        if pattern1 and pattern2:
            common_patterns = set(pattern1) & set(pattern2)
            if common_patterns:
                pattern_ratio = len(common_patterns) / max(len(pattern1), len(pattern2))
                if pattern_ratio >= 0.5:
                    return 0.6 + pattern_ratio * 0.2, "패턴매칭"
        
        # 6. 음성학적 유사도 (초성, 중성, 종성 분리)
        def extract_korean_features(text):
            features = []
            for char in text:
                if '가' <= char <= '힣':
                    # 한글 분해
                    code = ord(char) - ord('가')
                    cho = code // (21 * 28)  # 초성
                    jung = (code % (21 * 28)) // 28  # 중성
                    jong = code % 28  # 종성
                    features.extend([cho, jung, jong])
            return features
        
        features1 = extract_korean_features(text1)
        features2 = extract_korean_features(text2)
        
        if features1 and features2:
            common_features = len(set(features1) & set(features2))
            total_features = len(set(features1) | set(features2))
            if total_features > 0:
                feature_ratio = common_features / total_features
                if feature_ratio >= 0.6:
                    return feature_ratio * 0.5, "음성학적유사도"
        
        return 0, "매칭없음"

    # 매칭 수행
    print(f"\n🔍 고급 매칭 수행 중...")
    matching_results = []
    
    total_comparisons = len(df_a_clean) * len(df_b_clean)
    comparison_count = 0
    
    for idx_a, row_a in df_a_clean.iterrows():
        a_component = row_a[qaid_col_a]
        
        best_matches = []  # 상위 매칭 결과들 저장
        
        for idx_b, row_b in df_b_clean.iterrows():
            b_component = row_b[qaid_col_b]
            comparison_count += 1
            
            # 진행률 출력
            if comparison_count % 10000 == 0:
                progress = (comparison_count / total_comparisons) * 100
                print(f"    진행률: {progress:.1f}%")
            
            score, match_type = calculate_similarity(a_component, b_component)
            
            if score >= 0.3:  # 30% 이상 유사도
                best_matches.append({
                    'score': score,
                    'match_type': match_type,
                    'b_component': b_component,
                    'b_row': row_b
                })
        
        # 최고 점수 매칭만 선택
        if best_matches:
            best_match = max(best_matches, key=lambda x: x['score'])
            
            result_row = row_a.to_dict()
            result_row[f'B파일_{qaid_col_b}'] = best_match['b_component']
            result_row['매칭여부'] = '매칭됨'
            result_row['유사도점수'] = round(best_match['score'], 3)
            result_row['매칭방식'] = best_match['match_type']
            result_row['A파일_원본'] = a_component
            result_row['B파일_원본'] = best_match['b_component']
            
            # bar_cd 정보 추가
            result_row['A파일_bar_cd'] = row_a.get(barcode_col_a, 'N/A') if barcode_col_a else 'N/A'
            result_row['B파일_bar_cd'] = best_match['b_row'].get(barcode_col_b, 'N/A') if barcode_col_b else 'N/A'
            
            matching_results.append(result_row)

    print(f"\n📊 매칭 결과:")
    print(f"  - 총 매칭된 결과: {len(matching_results):,}개")
    
    if len(matching_results) > 0:
        # 결과 저장
        matching_df = pd.DataFrame(matching_results)
        
        # 매칭 방식별 통계
        match_stats = matching_df['매칭방식'].value_counts()
        print(f"\n📊 매칭 방식별 통계:")
        for method, count in match_stats.items():
            print(f"  - {method}: {count:,}개")
        
        # 매칭되지 않은 행들
        matched_components = set([result[qaid_col_a] for result in matching_results])
        unmatched_rows = df_a_clean[~df_a_clean[qaid_col_a].isin(matched_components)].copy()
        
        # 결과 저장
        with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
            matching_df.to_excel(writer, sheet_name='매칭결과', index=False)
            
            if len(unmatched_rows) > 0:
                unmatched_rows.to_excel(writer, sheet_name='매칭안된행', index=False)
            
            # 통계
            stats_df = pd.DataFrame({
                '매칭방식': match_stats.index,
                '개수': match_stats.values
            })
            stats_df.to_excel(writer, sheet_name='매칭통계', index=False)
            
            # 원본 데이터
            df_a.head(100).to_excel(writer, sheet_name='A파일원본', index=False)
            df_b.head(100).to_excel(writer, sheet_name='B파일원본', index=False)

        print(f"\n✅ 결과 파일 저장: {result_file}")
        
        # 샘플 출력
        print(f"\n📋 매칭 결과 샘플:")
        for i, result in enumerate(matching_results[:10]):
            print(f"  {i+1}. A: '{result['A파일_원본']}'")
            print(f"     B: '{result['B파일_원본']}'")
            print(f"     방식: {result['매칭방식']}, 점수: {result['유사도점수']}")
            print()
            
    else:
        print("\n❌ 매칭 결과가 없습니다.")
        
        # 매칭 실패 원인 분석
        print(f"\n🔍 매칭 실패 원인 분석:")
        
        # 길이 분석
        a_lengths = df_a_clean[qaid_col_a].str.len()
        b_lengths = df_b_clean[qaid_col_b].str.len()
        print(f"  - A파일 평균 길이: {a_lengths.mean():.1f}자")
        print(f"  - B파일 평균 길이: {b_lengths.mean():.1f}자")
        
        # 공통 단어 분석
        a_words = set()
        b_words = set()
        
        for text in df_a_clean[qaid_col_a]:
            a_words.update(re.findall(r'[가-힣A-Z0-9]+', text))
        
        for text in df_b_clean[qaid_col_b]:
            b_words.update(re.findall(r'[가-힣A-Z0-9]+', text))
        
        common_words = a_words & b_words
        print(f"  - A파일 고유 단어: {len(a_words):,}개")
        print(f"  - B파일 고유 단어: {len(b_words):,}개")
        print(f"  - 공통 단어: {len(common_words):,}개")
        
        if common_words:
            print(f"  - 공통 단어 샘플: {list(common_words)[:10]}")


# 함수 실행
if __name__ == "__main__":
    improved_match(
        file_a="A.xlsx",
        file_b="B.xlsx",
        result_file="improved_matched_result.xlsx",
        sheet_a="Sheet1",
        sheet_b="Sheet1",
        header_row=1
    )

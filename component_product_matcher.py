import pandas as pd
import numpy as np
from difflib import SequenceMatcher
import re


def count_component_items(file_path, result_file, sheet_name=None, header_row=0):
    """
    11.xlsx 파일의 "구성품" 컬럼의 각 항목의 개수를 세는 함수

    Parameters:
    - file_path: 분석할 엑셀 파일 경로 (11.xlsx)
    - result_file: 결과 저장 파일 경로
    - sheet_name: 시트명 (None이면 자동 탐지)
    - header_row: 헤더 행 번호 (0부터 시작)
    """
    print("📂 파일 읽기 및 구성품 항목 개수 분석 시작...")
    
    # 파일 및 시트 읽기
    try:
        excel_file = pd.ExcelFile(file_path)
        available_sheets = excel_file.sheet_names
        print(f"  - 사용 가능한 시트: {available_sheets}")
        
        if sheet_name is None or sheet_name not in available_sheets:
            sheet_name = available_sheets[0]
            print(f"  - 첫 번째 시트 '{sheet_name}' 사용")
        
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        print(f"  - 데이터 읽기 완료: {len(df):,}행 × {len(df.columns):,}열")
        
        # 필요한 컬럼 확인
        required_columns = ['구성품']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"❌ 다음 컬럼을 찾을 수 없습니다: {missing_columns}")
            print(f"📋 파일의 실제 컬럼: {list(df.columns)}")
            return

        print(f"✅ 필요한 컬럼 발견: {required_columns}")
        
    except Exception as e:
        print(f"❌ 파일 읽기 실패: {e}")
        return
    
    # 데이터 전처리
    print(f"\n🔧 데이터 전처리...")

    # 결측값 제거 및 문자열 변환
    df_clean = df.dropna(subset=['구성품']).copy()
    df_clean['구성품'] = df_clean['구성품'].astype(str).str.strip()

    # 빈 문자열 제거
    df_clean = df_clean[df_clean['구성품'] != '']

    print(f"  - 전처리 후 데이터: {len(df_clean):,}행")
    print(f"  - 고유 구성품 수: {df_clean['구성품'].nunique():,}개")
    
    # 구성품 개수 세기
    print(f"\n📊 구성품 항목별 개수 분석 중...")

    # 구성품별 개수 계산 (정확한 누적 계산)
    component_counts = df_clean['구성품'].value_counts().reset_index()
    component_counts.columns = ['구성품', '개수']

    # 순위 추가 (먼저 추가)
    component_counts['순위'] = range(1, len(component_counts) + 1)

    # 비율 계산 (소수점 2자리)
    total_records = len(df_clean)
    component_counts['비율'] = round((component_counts['개수'] / total_records) * 100, 2)

    # 누적개수 계산 (정확한 누적합)
    component_counts['누적개수'] = component_counts['개수'].cumsum()

    # 누적비율 계산 (누적개수 기준으로 정확히 계산)
    component_counts['누적비율'] = round((component_counts['누적개수'] / total_records) * 100, 2)

    # 컬럼 순서 재정렬
    component_counts = component_counts[['순위', '구성품', '개수', '비율', '누적개수', '누적비율']]

    # 누적 계산 검증
    print(f"  - 누적 계산 검증:")
    print(f"    • 전체 데이터 수: {total_records:,}")
    print(f"    • 마지막 누적개수: {component_counts['누적개수'].iloc[-1]:,}")
    print(f"    • 마지막 누적비율: {component_counts['누적비율'].iloc[-1]:.2f}%")

    if component_counts['누적개수'].iloc[-1] == total_records:
        print(f"    ✅ 누적개수 계산 정확")
    else:
        print(f"    ⚠️ 누적개수 불일치")

    if abs(component_counts['누적비율'].iloc[-1] - 100.0) < 0.01:
        print(f"    ✅ 누적비율 계산 정확")
    else:
        print(f"    ⚠️ 누적비율 불일치")
    
    # 구성품 분석
    print(f"  - 분석 완료!")

    # 계산식 설명 생성
    print(f"\n📋 계산식 설명 생성 중...")

    calculation_formulas = pd.DataFrame({
        '계산항목': [
            '개수',
            '비율',
            '누적개수',
            '누적비율',
            '순위'
        ],
        '계산식': [
            'COUNT(구성품별 출현 횟수)',
            '(개수 / 전체데이터수) × 100',
            'SUM(1번째부터 현재순위까지의 개수)',
            '(누적개수 / 전체데이터수) × 100',
            'RANK(개수 기준 내림차순)'
        ],
        '설명': [
            '해당 구성품이 데이터에서 나타나는 총 횟수',
            '해당 구성품이 전체 데이터에서 차지하는 비율(%)',
            '1순위부터 현재 순위까지의 개수를 모두 합한 값',
            '누적개수가 전체 데이터에서 차지하는 비율(%)',
            '개수를 기준으로 내림차순 정렬한 순위'
        ],
        '예시': [
            "구성품 'A'가 25번 나타나면 개수 = 25",
            "전체 500개 중 25개면 비율 = (25/500)×100 = 5.0%",
            "1위(25개) + 2위(20개) + 3위(15개) = 60개",
            "누적개수 60개면 누적비율 = (60/500)×100 = 12.0%",
            "개수가 가장 많은 구성품이 1위"
        ],
        '검증방법': [
            '모든 개수의 합 = 전체 데이터 행수',
            '모든 비율의 합 = 100%',
            '마지막 누적개수 = 전체 데이터 행수',
            '마지막 누적비율 = 100%',
            '순위는 1부터 고유구성품수까지 연속'
        ]
    })
    
    # 통계 분석
    print(f"\n📊 구성품 개수 분석 결과...")

    total_components = len(component_counts)
    total_records = len(df_clean)

    print(f"  - 전체 데이터 행수: {total_records:,}개")
    print(f"  - 고유 구성품 수: {total_components:,}개")
    print(f"  - 평균 구성품당 개수: {total_records/total_components:.1f}개")

    # 상위 구성품
    top_components = component_counts.head(10)
    print(f"\n🔝 개수가 많은 구성품 TOP 10:")
    for _, row in top_components.iterrows():
        print(f"  - {row['순위']}위: '{row['구성품']}' - {row['개수']}개 ({row['비율']}%) [누적: {row['누적개수']}개, {row['누적비율']}%]")

    # 개수별 분포 분석
    count_distribution = pd.DataFrame({
        '개수구간': ['1개', '2-5개', '6-10개', '11-20개', '21-50개', '51개이상'],
        '구성품수': [
            len(component_counts[component_counts['개수'] == 1]),
            len(component_counts[(component_counts['개수'] >= 2) & (component_counts['개수'] <= 5)]),
            len(component_counts[(component_counts['개수'] >= 6) & (component_counts['개수'] <= 10)]),
            len(component_counts[(component_counts['개수'] >= 11) & (component_counts['개수'] <= 20)]),
            len(component_counts[(component_counts['개수'] >= 21) & (component_counts['개수'] <= 50)]),
            len(component_counts[component_counts['개수'] >= 51])
        ]
    })

    # 분포별 비율 추가
    count_distribution['비율'] = round((count_distribution['구성품수'] / total_components) * 100, 2)

    print(f"\n📈 개수별 구성품 분포:")
    for _, row in count_distribution.iterrows():
        print(f"  - {row['개수구간']}: {row['구성품수']}개 구성품 ({row['비율']}%)")

    # 요약 통계 생성
    summary_stats = pd.DataFrame([
        ['전체 데이터 행수', total_records],
        ['고유 구성품 수', total_components],
        ['평균 구성품당 개수', round(total_records/total_components, 1)],
        ['최다 개수 구성품', component_counts.iloc[0]['구성품']],
        ['최다 개수', component_counts.iloc[0]['개수']],
        ['최다 개수 비율', f"{component_counts.iloc[0]['비율']}%"],
        ['', ''],
        ['개수 합계 검증', component_counts['개수'].sum()],
        ['비율 합계 검증', f"{component_counts['비율'].sum():.2f}%"],
        ['누적개수 최종값', component_counts['누적개수'].iloc[-1]],
        ['누적비율 최종값', f"{component_counts['누적비율'].iloc[-1]:.2f}%"],
        ['', ''],
        ['단일개수 구성품수', len(component_counts[component_counts['개수'] == 1])],
        ['고빈도 구성품수 (10개이상)', len(component_counts[component_counts['개수'] >= 10])]
    ], columns=['항목', '값'])
    
    # 엑셀 저장 (지정된 시트만)
    print(f"\n💾 결과 저장 중...")

    with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
        # 1. 메인 결과 - 구성품별 개수
        component_counts.to_excel(writer, sheet_name='구성품별개수', index=False)

        # 2. 요약 통계
        summary_stats.to_excel(writer, sheet_name='요약통계', index=False)

        # 3. 개수별 분포 분석
        count_distribution.to_excel(writer, sheet_name='개수별분포', index=False)

        # 4. 상위 50개 구성품
        top_50_components = component_counts.head(50)
        top_50_components.to_excel(writer, sheet_name='상위50개구성품', index=False)

        # 5. 단일 개수 구성품 (개수가 1개인 것들)
        single_count = component_counts[component_counts['개수'] == 1]
        single_count.to_excel(writer, sheet_name='단일개수구성품', index=False)

        # 6. 고빈도 구성품 (개수 >= 10)
        high_frequency = component_counts[component_counts['개수'] >= 10]
        high_frequency.to_excel(writer, sheet_name='고빈도구성품', index=False)

        # 7. 계산식 설명
        calculation_formulas.to_excel(writer, sheet_name='계산식', index=False)
    
    print(f"✅ 저장 완료: {result_file}")
    print(f"📋 저장된 시트 (총 7개):")
    print(f"  1. '구성품별개수': 모든 구성품의 개수 결과 ({len(component_counts):,}개)")
    print(f"  2. '요약통계': 전체 개수 통계 및 검증")
    print(f"  3. '개수별분포': 개수별 구성품 분포")
    print(f"  4. '상위50개구성품': 개수가 많은 상위 50개 구성품")
    print(f"  5. '단일개수구성품': 개수가 1개인 구성품들 ({len(single_count):,}개)")
    print(f"  6. '고빈도구성품': 개수가 10개 이상인 구성품들 ({len(high_frequency):,}개)")
    print(f"  7. '계산식': 모든 계산 공식과 설명")

    # 누적 계산 최종 검증 출력
    print(f"\n🔍 최종 검증 결과:")
    print(f"  - 개수 합계: {component_counts['개수'].sum():,} (전체 데이터: {total_records:,})")
    print(f"  - 비율 합계: {component_counts['비율'].sum():.2f}% (목표: 100.00%)")
    print(f"  - 누적개수 최종: {component_counts['누적개수'].iloc[-1]:,} (전체 데이터: {total_records:,})")
    print(f"  - 누적비율 최종: {component_counts['누적비율'].iloc[-1]:.2f}% (목표: 100.00%)")

    # 샘플 결과 출력 (누적 정보 포함)
    print(f"\n📋 구성품 개수 결과 샘플 (상위 10개):")
    for i, (_, row) in enumerate(component_counts.head(10).iterrows()):
        print(f"  {row['순위']:2d}위. '{row['구성품']}': {row['개수']:,}개 ({row['비율']}%) [누적: {row['누적개수']:,}개, {row['누적비율']}%]")

    # 하위 구성품도 출력
    if len(component_counts) > 10:
        print(f"\n📋 구성품 개수 결과 샘플 (하위 3개):")
        for i, (_, row) in enumerate(component_counts.tail(3).iterrows()):
            print(f"  {row['순위']:2d}위. '{row['구성품']}': {row['개수']:,}개 ({row['비율']}%) [누적: {row['누적개수']:,}개, {row['누적비율']}%]")

    return component_counts


# 함수 실행
if __name__ == "__main__":
    count_component_items(
        file_path="11.xlsx",  # 분석할 파일
        result_file="component_count_result.xlsx",  # 결과 파일
        sheet_name=None,  # 자동 탐지
        header_row=1  # 헤더 행 번호
    )
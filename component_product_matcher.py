import pandas as pd
import numpy as np
from difflib import SequenceMatcher
import re


def count_component_items(file_path, result_file, sheet_name=None, header_row=0):
    """
    11.xlsx 파일의 "구성품" 컬럼의 각 항목의 개수를 세는 함수

    Parameters:
    - file_path: 분석할 엑셀 파일 경로 (11.xlsx)
    - result_file: 결과 저장 파일 경로
    - sheet_name: 시트명 (None이면 자동 탐지)
    - header_row: 헤더 행 번호 (0부터 시작)
    """
    print("📂 파일 읽기 및 구성품 항목 개수 분석 시작...")
    
    # 파일 및 시트 읽기
    try:
        excel_file = pd.ExcelFile(file_path)
        available_sheets = excel_file.sheet_names
        print(f"  - 사용 가능한 시트: {available_sheets}")
        
        if sheet_name is None or sheet_name not in available_sheets:
            sheet_name = available_sheets[0]
            print(f"  - 첫 번째 시트 '{sheet_name}' 사용")
        
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        print(f"  - 데이터 읽기 완료: {len(df):,}행 × {len(df.columns):,}열")
        
        # 필요한 컬럼 확인
        required_columns = ['구성품']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"❌ 다음 컬럼을 찾을 수 없습니다: {missing_columns}")
            print(f"📋 파일의 실제 컬럼: {list(df.columns)}")
            return

        print(f"✅ 필요한 컬럼 발견: {required_columns}")
        
    except Exception as e:
        print(f"❌ 파일 읽기 실패: {e}")
        return
    
    # 데이터 전처리
    print(f"\n🔧 데이터 전처리...")

    # 결측값 제거 및 문자열 변환
    df_clean = df.dropna(subset=['구성품']).copy()
    df_clean['구성품'] = df_clean['구성품'].astype(str).str.strip()

    # 빈 문자열 제거
    df_clean = df_clean[df_clean['구성품'] != '']

    print(f"  - 전처리 후 데이터: {len(df_clean):,}행")
    print(f"  - 고유 구성품 수: {df_clean['구성품'].nunique():,}개")
    
    # 구성품 개수 세기
    print(f"\n📊 구성품 항목별 개수 분석 중...")

    # 구성품별 개수 계산 (정확한 누적 계산)
    component_counts = df_clean['구성품'].value_counts().reset_index()
    component_counts.columns = ['구성품', '개수']

    # 순위 추가 (먼저 추가)
    component_counts['순위'] = range(1, len(component_counts) + 1)

    # 비율 계산 (소수점 2자리)
    total_records = len(df_clean)
    component_counts['비율'] = round((component_counts['개수'] / total_records) * 100, 2)

    # 누적개수 계산 (정확한 누적합)
    component_counts['누적개수'] = component_counts['개수'].cumsum()

    # 누적비율 계산 (누적개수 기준으로 정확히 계산)
    component_counts['누적비율'] = round((component_counts['누적개수'] / total_records) * 100, 2)

    # 컬럼 순서 재정렬
    component_counts = component_counts[['순위', '구성품', '개수', '비율', '누적개수', '누적비율']]

    # 누적 계산 검증
    print(f"  - 누적 계산 검증:")
    print(f"    • 전체 데이터 수: {total_records:,}")
    print(f"    • 마지막 누적개수: {component_counts['누적개수'].iloc[-1]:,}")
    print(f"    • 마지막 누적비율: {component_counts['누적비율'].iloc[-1]:.2f}%")

    if component_counts['누적개수'].iloc[-1] == total_records:
        print(f"    ✅ 누적개수 계산 정확")
    else:
        print(f"    ⚠️ 누적개수 불일치")

    if abs(component_counts['누적비율'].iloc[-1] - 100.0) < 0.01:
        print(f"    ✅ 누적비율 계산 정확")
    else:
        print(f"    ⚠️ 누적비율 불일치")
    
    # 구성품 분석
    print(f"  - 분석 완료!")

    # 텍스트 분석 추가
    print(f"\n🔍 구성품 텍스트 분석 중...")

    text_analysis = []
    for _, row in component_counts.iterrows():
        component = row['구성품']
        count = row['개수']

        # 텍스트 길이 분석
        text_length = len(component)

        # 문자 유형 분석
        korean_chars = len(re.findall(r'[가-힣]', component))
        english_chars = len(re.findall(r'[A-Za-z]', component))
        number_chars = len(re.findall(r'[0-9]', component))
        special_chars = len(re.findall(r'[^가-힣A-Za-z0-9\s]', component))

        # 단어 개수
        words = re.findall(r'\w+', component)
        word_count = len(words)

        text_analysis.append({
            '구성품': component,
            '개수': count,
            '비율': row['비율'],
            '텍스트길이': text_length,
            '한글문자수': korean_chars,
            '영문문자수': english_chars,
            '숫자문자수': number_chars,
            '특수문자수': special_chars,
            '단어개수': word_count,
            '문자유형': '한글' if korean_chars > 0 and english_chars == 0 else
                      '영문' if english_chars > 0 and korean_chars == 0 else
                      '혼합' if korean_chars > 0 and english_chars > 0 else '기타'
        })

    text_analysis_df = pd.DataFrame(text_analysis)
    
    # 통계 분석
    print(f"\n📊 구성품 개수 분석 결과...")

    total_components = len(component_counts)
    total_records = len(df_clean)

    print(f"  - 전체 데이터 행수: {total_records:,}개")
    print(f"  - 고유 구성품 수: {total_components:,}개")
    print(f"  - 평균 구성품당 개수: {total_records/total_components:.1f}개")

    # 상위 구성품
    top_components = component_counts.head(10)
    print(f"\n🔝 개수가 많은 구성품 TOP 10:")
    for _, row in top_components.iterrows():
        print(f"  - {row['순위']}위: '{row['구성품']}' - {row['개수']}개 ({row['비율']}%)")

    # 개수별 분포 분석
    count_distribution = pd.DataFrame({
        '개수구간': ['1개', '2-5개', '6-10개', '11-20개', '21-50개', '51개이상'],
        '구성품수': [
            len(component_counts[component_counts['개수'] == 1]),
            len(component_counts[(component_counts['개수'] >= 2) & (component_counts['개수'] <= 5)]),
            len(component_counts[(component_counts['개수'] >= 6) & (component_counts['개수'] <= 10)]),
            len(component_counts[(component_counts['개수'] >= 11) & (component_counts['개수'] <= 20)]),
            len(component_counts[(component_counts['개수'] >= 21) & (component_counts['개수'] <= 50)]),
            len(component_counts[component_counts['개수'] >= 51])
        ]
    })

    print(f"\n📈 개수별 구성품 분포:")
    for _, row in count_distribution.iterrows():
        print(f"  - {row['개수구간']}: {row['구성품수']}개 구성품")

    # 텍스트 분석 통계
    text_stats = {
        '평균텍스트길이': text_analysis_df['텍스트길이'].mean(),
        '최장텍스트길이': text_analysis_df['텍스트길이'].max(),
        '최단텍스트길이': text_analysis_df['텍스트길이'].min(),
        '한글전용구성품': len(text_analysis_df[text_analysis_df['문자유형'] == '한글']),
        '영문전용구성품': len(text_analysis_df[text_analysis_df['문자유형'] == '영문']),
        '혼합문자구성품': len(text_analysis_df[text_analysis_df['문자유형'] == '혼합'])
    }

    print(f"\n📝 텍스트 분석 통계:")
    print(f"  - 평균 텍스트 길이: {text_stats['평균텍스트길이']:.1f}자")
    print(f"  - 최장 텍스트 길이: {text_stats['최장텍스트길이']}자")
    print(f"  - 최단 텍스트 길이: {text_stats['최단텍스트길이']}자")
    print(f"  - 한글 전용: {text_stats['한글전용구성품']}개")
    print(f"  - 영문 전용: {text_stats['영문전용구성품']}개")
    print(f"  - 혼합 문자: {text_stats['혼합문자구성품']}개")

    # 요약 통계 생성
    summary_stats = pd.DataFrame([
        ['전체 데이터 행수', total_records],
        ['고유 구성품 수', total_components],
        ['평균 구성품당 개수', round(total_records/total_components, 1)],
        ['최다 개수 구성품', component_counts.iloc[0]['구성품']],
        ['최다 개수', component_counts.iloc[0]['개수']],
        ['', ''],
        ['평균 텍스트 길이', round(text_stats['평균텍스트길이'], 1)],
        ['최장 텍스트 길이', text_stats['최장텍스트길이']],
        ['최단 텍스트 길이', text_stats['최단텍스트길이']],
        ['', ''],
        ['한글 전용 구성품', text_stats['한글전용구성품']],
        ['영문 전용 구성품', text_stats['영문전용구성품']],
        ['혼합 문자 구성품', text_stats['혼합문자구성품']]
    ], columns=['항목', '값'])
    
    # 엑셀 저장
    print(f"\n💾 결과 저장 중...")

    with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
        # 메인 결과 - 구성품별 개수
        component_counts.to_excel(writer, sheet_name='구성품별개수', index=False)

        # 텍스트 분석 결과
        text_analysis_df.to_excel(writer, sheet_name='텍스트분석', index=False)

        # 요약 통계
        summary_stats.to_excel(writer, sheet_name='요약통계', index=False)

        # 개수별 분포 분석
        count_distribution.to_excel(writer, sheet_name='개수별분포', index=False)

        # 상위 구성품 (개수 기준)
        top_50_components = component_counts.head(50)
        top_50_components.to_excel(writer, sheet_name='상위50개구성품', index=False)

        # 단일 개수 구성품 (개수가 1개인 것들)
        single_count = component_counts[component_counts['개수'] == 1]
        if len(single_count) > 0:
            single_count.to_excel(writer, sheet_name='단일개수구성품', index=False)

        # 고빈도 구성품 (개수 >= 10)
        high_frequency = component_counts[component_counts['개수'] >= 10]
        if len(high_frequency) > 0:
            high_frequency.to_excel(writer, sheet_name='고빈도구성품', index=False)

        # 문자 유형별 분류
        char_type_summary = text_analysis_df.groupby('문자유형').agg({
            '개수': ['count', 'sum', 'mean'],
            '텍스트길이': ['mean', 'max', 'min']
        }).round(2)
        char_type_summary.to_excel(writer, sheet_name='문자유형별분석')

        # 원본 데이터 샘플
        sample_df = df_clean.head(1000)
        sample_df.to_excel(writer, sheet_name='원본데이터샘플', index=False)
    
    print(f"✅ 저장 완료: {result_file}")
    print(f"📋 저장된 시트:")
    print(f"  - '구성품별개수': 모든 구성품의 개수 결과 ({len(component_counts):,}개)")
    print(f"  - '텍스트분석': 구성품 텍스트 상세 분석")
    print(f"  - '요약통계': 전체 개수 통계")
    print(f"  - '개수별분포': 개수별 구성품 분포")
    print(f"  - '상위50개구성품': 개수가 많은 상위 50개 구성품")
    if len(single_count) > 0:
        print(f"  - '단일개수구성품': 개수가 1개인 구성품들 ({len(single_count):,}개)")
    if len(high_frequency) > 0:
        print(f"  - '고빈도구성품': 개수가 10개 이상인 구성품들 ({len(high_frequency):,}개)")
    print(f"  - '문자유형별분석': 한글/영문/혼합 유형별 분석")
    print(f"  - '원본데이터샘플': 원본 데이터 샘플")

    # 샘플 결과 출력
    print(f"\n📋 구성품 개수 결과 샘플 (상위 10개):")
    for i, (_, row) in enumerate(component_counts.head(10).iterrows()):
        print(f"  {row['순위']:2d}위. '{row['구성품']}': {row['개수']:,}개 ({row['비율']}%)")

    # 하위 구성품도 출력
    if len(component_counts) > 10:
        print(f"\n📋 구성품 개수 결과 샘플 (하위 5개):")
        for i, (_, row) in enumerate(component_counts.tail(5).iterrows()):
            print(f"  {row['순위']:2d}위. '{row['구성품']}': {row['개수']:,}개 ({row['비율']}%)")

    return component_counts


# 함수 실행
if __name__ == "__main__":
    count_component_items(
        file_path="11.xlsx",  # 분석할 파일
        result_file="component_count_result.xlsx",  # 결과 파일
        sheet_name=None,  # 자동 탐지
        header_row=1  # 헤더 행 번호
    )

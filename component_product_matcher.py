import pandas as pd
import numpy as np
from difflib import SequenceMatcher
import re


def count_component_items(file_path, result_file, sheet_name=None, header_row=0):
    """
    11.xlsx 파일의 "구성품" 컬럼의 각 항목의 개수를 세는 함수

    Parameters:
    - file_path: 분석할 엑셀 파일 경로 (11.xlsx)
    - result_file: 결과 저장 파일 경로
    - sheet_name: 시트명 (None이면 자동 탐지)
    - header_row: 헤더 행 번호 (0부터 시작)
    """
    print("📂 파일 읽기 및 구성품 항목 개수 분석 시작...")
    
    # 파일 및 시트 읽기
    try:
        excel_file = pd.ExcelFile(file_path)
        available_sheets = excel_file.sheet_names
        print(f"  - 사용 가능한 시트: {available_sheets}")
        
        if sheet_name is None or sheet_name not in available_sheets:
            sheet_name = available_sheets[0]
            print(f"  - 첫 번째 시트 '{sheet_name}' 사용")
        
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        print(f"  - 데이터 읽기 완료: {len(df):,}행 × {len(df.columns):,}열")
        
        # 필요한 컬럼 확인
        required_columns = ['구성품']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"❌ 다음 컬럼을 찾을 수 없습니다: {missing_columns}")
            print(f"📋 파일의 실제 컬럼: {list(df.columns)}")
            return

        print(f"✅ 필요한 컬럼 발견: {required_columns}")
        
    except Exception as e:
        print(f"❌ 파일 읽기 실패: {e}")
        return
    
    # 데이터 전처리
    print(f"\n🔧 데이터 전처리...")

    # 결측값 제거 및 문자열 변환
    df_clean = df.dropna(subset=['구성품']).copy()
    df_clean['구성품'] = df_clean['구성품'].astype(str).str.strip()

    # 빈 문자열 제거
    df_clean = df_clean[df_clean['구성품'] != '']

    print(f"  - 전처리 후 데이터: {len(df_clean):,}행")
    print(f"  - 고유 구성품 수: {df_clean['구성품'].nunique():,}개")
    
    # 구성품 개수 세기
    print(f"\n📊 구성품 항목별 개수 분석 중...")

    # 구성품별 개수 계산
    component_counts = df_clean['구성품'].value_counts().reset_index()
    component_counts.columns = ['구성품', '개수']

    # 추가 분석을 위한 정보
    component_counts['비율'] = round((component_counts['개수'] / len(df_clean)) * 100, 2)
    component_counts['누적개수'] = component_counts['개수'].cumsum()
    component_counts['누적비율'] = round((component_counts['누적개수'] / len(df_clean)) * 100, 2)

    # 순위 추가
    component_counts['순위'] = range(1, len(component_counts) + 1)

    # 컬럼 순서 재정렬
    component_counts = component_counts[['순위', '구성품', '개수', '비율', '누적개수', '누적비율']]
    
    # 구성품별 매칭 분석
    print(f"\n🔍 구성품별 제품명 매칭 분석 중...")
    
    unique_components = df_clean['구성품'].unique()
    unique_products = df_clean['제품명'].unique()
    
    matching_results = []
    
    for i, component in enumerate(unique_components):
        if i % 50 == 0:
            print(f"  - 진행률: {i+1}/{len(unique_components)} ({((i+1)/len(unique_components)*100):.1f}%)")
        
        # 해당 구성품과 매칭되는 제품명들 찾기
        matches = []
        
        for product in unique_products:
            similarity = calculate_similarity(component, product)
            if similarity >= 0.3:  # 30% 이상 유사도
                matches.append({
                    '제품명': product,
                    '유사도': round(similarity, 3),
                    '매칭타입': '정확일치' if similarity == 1.0 else 
                               '포함관계' if similarity == 0.9 else
                               '높은유사도' if similarity >= 0.7 else
                               '중간유사도' if similarity >= 0.5 else '낮은유사도'
                })
        
        # 유사도 순으로 정렬
        matches.sort(key=lambda x: x['유사도'], reverse=True)
        
        # 결과 저장
        result = {
            '구성품': component,
            '매칭된제품명수': len(matches),
            '최고유사도': matches[0]['유사도'] if matches else 0,
            '최고유사도제품명': matches[0]['제품명'] if matches else 'N/A',
            '매칭타입': matches[0]['매칭타입'] if matches else 'N/A',
            '정확일치수': len([m for m in matches if m['매칭타입'] == '정확일치']),
            '포함관계수': len([m for m in matches if m['매칭타입'] == '포함관계']),
            '높은유사도수': len([m for m in matches if m['매칭타입'] == '높은유사도']),
            '중간유사도수': len([m for m in matches if m['매칭타입'] == '중간유사도']),
            '낮은유사도수': len([m for m in matches if m['매칭타입'] == '낮은유사도']),
            '상위3개매칭제품명': ' | '.join([f"{m['제품명']}({m['유사도']})" for m in matches[:3]])
        }
        
        matching_results.append(result)
    
    # 결과 데이터프레임 생성
    results_df = pd.DataFrame(matching_results)
    
    # 통계 분석
    print(f"\n📊 매칭 결과 분석...")
    
    total_components = len(results_df)
    matched_components = len(results_df[results_df['매칭된제품명수'] > 0])
    unmatched_components = total_components - matched_components
    
    print(f"  - 전체 구성품 수: {total_components:,}개")
    print(f"  - 매칭된 구성품 수: {matched_components:,}개 ({matched_components/total_components*100:.1f}%)")
    print(f"  - 매칭되지 않은 구성품 수: {unmatched_components:,}개 ({unmatched_components/total_components*100:.1f}%)")
    
    # 매칭 타입별 통계
    match_type_stats = {
        '정확일치': results_df['정확일치수'].sum(),
        '포함관계': results_df['포함관계수'].sum(),
        '높은유사도': results_df['높은유사도수'].sum(),
        '중간유사도': results_df['중간유사도수'].sum(),
        '낮은유사도': results_df['낮은유사도수'].sum()
    }
    
    print(f"\n📈 매칭 타입별 통계:")
    for match_type, count in match_type_stats.items():
        print(f"  - {match_type}: {count:,}개")
    
    # 상위/하위 매칭 구성품
    top_matched = results_df.nlargest(10, '매칭된제품명수')
    unmatched = results_df[results_df['매칭된제품명수'] == 0]
    
    print(f"\n🔝 매칭 제품명이 많은 구성품 TOP 10:")
    for _, row in top_matched.iterrows():
        print(f"  - '{row['구성품']}': {row['매칭된제품명수']}개 매칭")
    
    if len(unmatched) > 0:
        print(f"\n❌ 매칭되지 않은 구성품 ({len(unmatched)}개):")
        for _, row in unmatched.head(10).iterrows():
            print(f"  - '{row['구성품']}'")
    
    # 요약 통계 생성
    summary_stats = pd.DataFrame([
        ['전체 구성품 수', total_components],
        ['매칭된 구성품 수', matched_components],
        ['매칭되지 않은 구성품 수', unmatched_components],
        ['매칭 성공률 (%)', round(matched_components/total_components*100, 2)],
        ['평균 매칭 제품명 수', round(results_df['매칭된제품명수'].mean(), 2)],
        ['최대 매칭 제품명 수', results_df['매칭된제품명수'].max()],
        ['', ''],
        ['정확일치 총 개수', match_type_stats['정확일치']],
        ['포함관계 총 개수', match_type_stats['포함관계']],
        ['높은유사도 총 개수', match_type_stats['높은유사도']],
        ['중간유사도 총 개수', match_type_stats['중간유사도']],
        ['낮은유사도 총 개수', match_type_stats['낮은유사도']]
    ], columns=['항목', '값'])
    
    # 매칭 분포 분석
    distribution_analysis = pd.DataFrame({
        '매칭수구간': ['0개', '1개', '2-5개', '6-10개', '11-20개', '21개이상'],
        '구성품수': [
            len(results_df[results_df['매칭된제품명수'] == 0]),
            len(results_df[results_df['매칭된제품명수'] == 1]),
            len(results_df[(results_df['매칭된제품명수'] >= 2) & (results_df['매칭된제품명수'] <= 5)]),
            len(results_df[(results_df['매칭된제품명수'] >= 6) & (results_df['매칭된제품명수'] <= 10)]),
            len(results_df[(results_df['매칭된제품명수'] >= 11) & (results_df['매칭된제품명수'] <= 20)]),
            len(results_df[results_df['매칭된제품명수'] >= 21])
        ]
    })
    
    # 엑셀 저장
    print(f"\n💾 결과 저장 중...")
    
    with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
        # 메인 결과
        results_df.to_excel(writer, sheet_name='구성품별매칭결과', index=False)
        
        # 요약 통계
        summary_stats.to_excel(writer, sheet_name='요약통계', index=False)
        
        # 매칭 분포 분석
        distribution_analysis.to_excel(writer, sheet_name='매칭분포분석', index=False)
        
        # 매칭 성공 구성품 (매칭수 > 0)
        successful_matches = results_df[results_df['매칭된제품명수'] > 0].sort_values('매칭된제품명수', ascending=False)
        successful_matches.to_excel(writer, sheet_name='매칭성공구성품', index=False)
        
        # 매칭 실패 구성품 (매칭수 = 0)
        failed_matches = results_df[results_df['매칭된제품명수'] == 0]
        if len(failed_matches) > 0:
            failed_matches.to_excel(writer, sheet_name='매칭실패구성품', index=False)
        
        # 높은 매칭률 구성품 (매칭수 >= 10)
        high_matches = results_df[results_df['매칭된제품명수'] >= 10].sort_values('매칭된제품명수', ascending=False)
        if len(high_matches) > 0:
            high_matches.to_excel(writer, sheet_name='높은매칭률구성품', index=False)
        
        # 원본 데이터 샘플
        sample_df = df_clean.head(1000)
        sample_df.to_excel(writer, sheet_name='원본데이터샘플', index=False)
    
    print(f"✅ 저장 완료: {result_file}")
    print(f"📋 저장된 시트:")
    print(f"  - '구성품별매칭결과': 모든 구성품의 매칭 결과 ({len(results_df):,}개)")
    print(f"  - '요약통계': 전체 매칭 통계")
    print(f"  - '매칭분포분석': 매칭 수별 분포")
    print(f"  - '매칭성공구성품': 매칭된 구성품들 ({len(successful_matches):,}개)")
    if len(failed_matches) > 0:
        print(f"  - '매칭실패구성품': 매칭되지 않은 구성품들 ({len(failed_matches):,}개)")
    if len(high_matches) > 0:
        print(f"  - '높은매칭률구성품': 10개 이상 매칭된 구성품들 ({len(high_matches):,}개)")
    print(f"  - '원본데이터샘플': 원본 데이터 샘플")
    
    # 샘플 결과 출력
    print(f"\n📋 매칭 결과 샘플 (상위 5개):")
    for i, (_, row) in enumerate(results_df.head(5).iterrows()):
        print(f"\n  {i+1}. 구성품: '{row['구성품']}'")
        print(f"     매칭된 제품명 수: {row['매칭된제품명수']}개")
        print(f"     최고 유사도: {row['최고유사도']} ({row['매칭타입']})")
        print(f"     최고 유사도 제품명: '{row['최고유사도제품명']}'")
        if row['상위3개매칭제품명'] != '':
            print(f"     상위 3개 매칭: {row['상위3개매칭제품명']}")
    
    return results_df


# 함수 실행
if __name__ == "__main__":
    match_components_to_products(
        file_path="11.xlsx",  # 분석할 파일
        result_file="component_product_matching_result.xlsx",  # 결과 파일
        sheet_name=None,  # 자동 탐지
        header_row=1  # 헤더 행 번호
    )

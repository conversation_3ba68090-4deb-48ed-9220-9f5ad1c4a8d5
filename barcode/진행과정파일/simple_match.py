import pandas as pd

def simple_match(file_a, file_b, result_file):
    """
    가장 간단한 매칭 방식 - 모든 조합을 확인
    """
    print("📂 파일 읽기...")
    df_a = pd.read_excel(file_a, header=1)
    df_b = pd.read_excel(file_b, header=1)
    
    print(f"A파일: {len(df_a)}행, B파일: {len(df_b)}행")
    print(f"A파일 컬럼: {list(df_a.columns)}")
    print(f"B파일 컬럼: {list(df_b.columns)}")
    
    # 구성품 컬럼 찾기
    a_col = None
    b_col = None
    
    for col in df_a.columns:
        if '구성품' in str(col):
            a_col = col
            break
    
    for col in df_b.columns:
        if '구성품' in str(col):
            b_col = col
            break
    
    if not a_col or not b_col:
        print("구성품 컬럼을 찾을 수 없습니다.")
        return
    
    print(f"A파일 구성품 컬럼: {a_col}")
    print(f"B파일 구성품 컬럼: {b_col}")
    
    # 데이터 정리
    df_a[a_col] = df_a[a_col].fillna('').astype(str)
    df_b[b_col] = df_b[b_col].fillna('').astype(str)
    
    # 샘플 출력
    print("\nA파일 구성품 샘플:")
    for i, val in enumerate(df_a[a_col].head(5)):
        print(f"  {i+1}. '{val}'")
    
    print("\nB파일 구성품 샘플:")
    for i, val in enumerate(df_b[b_col].head(5)):
        print(f"  {i+1}. '{val}'")
    
    # 매칭 시도
    results = []
    
    for i, row_a in df_a.iterrows():
        a_val = str(row_a[a_col]).strip()
        if not a_val or a_val == 'nan':
            continue
            
        for j, row_b in df_b.iterrows():
            b_val = str(row_b[b_col]).strip()
            if not b_val or b_val == 'nan':
                continue
            
            # 간단한 포함 관계 확인
            if a_val.lower() in b_val.lower() or b_val.lower() in a_val.lower():
                result = row_a.to_dict()
                result[f'B_{b_col}'] = b_val
                result['매칭타입'] = '포함관계'
                results.append(result)
                break  # 첫 번째 매칭만 사용
    
    print(f"\n매칭 결과: {len(results)}개")
    
    if results:
        result_df = pd.DataFrame(results)
        result_df.to_excel(result_file, index=False)
        print(f"결과 저장: {result_file}")
        
        # 샘플 출력
        for i, result in enumerate(results[:3]):
            print(f"  {i+1}. A: '{result[a_col][:30]}...' ↔ B: '{result[f'B_{b_col}'][:30]}...'")
    else:
        # 매칭 실패 시 원본 데이터 저장
        with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
            df_a.to_excel(writer, sheet_name='A파일원본', index=False)
            df_b.to_excel(writer, sheet_name='B파일원본', index=False)
        print(f"매칭 실패. 원본 데이터 저장: {result_file}")

if __name__ == "__main__":
    simple_match("A.xlsx", "B.xlsx", "simple_result.xlsx")

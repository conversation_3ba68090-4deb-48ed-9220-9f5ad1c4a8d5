import pandas as pd


def match_by_qaid(file_a, file_b, result_file,
                  sheet_a='Sheet1', sheet_b='Sheet1',
                  qaid_col_a='구성품', qaid_col_b='구성품',
                  header_row=1, keywords=None):
    """
    파일 A를 기준으로 파일 B에서 QAID가 일치하는 데이터만 추출하여 새 파일 생성

    Parameters:
    - file_a: 기준이 되는 파일 경로
    - file_b: 매칭할 대상 파일 경로
    - result_file: 결과 저장 파일 경로
    - sheet_a, sheet_b: 각 파일의 시트명
    - qaid_col_a, qaid_col_b: 각 파일의 QAID 컬럼명
    - header_row: 엑셀 헤더 행 번호 (0부터 시작)
    - keywords: 구성품에서 검색할 특정 단어 리스트 (None이면 정확히 일치하는 문자열로 매칭)
    """
    # 파일 읽기
    df_a = pd.read_excel(file_a, sheet_name=sheet_a, header=header_row)
    df_b = pd.read_excel(file_b, sheet_name=sheet_b, header=header_row)

    # 컬럼 존재 여부 확인
    if qaid_col_a not in df_a.columns:
        raise KeyError(f"파일 A에 '{qaid_col_a}' 컬럼이 없습니다. 사용 가능한 컬럼: {list(df_a.columns)}")
    if qaid_col_b not in df_b.columns:
        raise KeyError(f"파일 B에 '{qaid_col_b}' 컬럼이 없습니다. 사용 가능한 컬럼: {list(df_b.columns)}")

    # 구성품 컬럼 전처리 (NaN 값 처리 포함)
    df_a[qaid_col_a] = df_a[qaid_col_a].fillna('').astype(str).str.strip()
    df_b[qaid_col_b] = df_b[qaid_col_b].fillna('').astype(str).str.strip()

    # 키워드 매칭 방식 사용
    if keywords:
        # 단일 키워드를 리스트로 변환
        if isinstance(keywords, str):
            keywords = [keywords]

        print(f"🔍 키워드 검색: {keywords}")

        # 디버깅: 데이터 샘플 확인
        print(f"📋 파일 A 데이터 샘플 (컬럼: {qaid_col_a}):")
        for idx, value in df_a[qaid_col_a].head(10).items():
            print(f"  [{idx}] '{value}' (타입: {type(value)})")

        print(f"📋 파일 B 데이터 샘플 (컬럼: {qaid_col_b}):")
        for idx, value in df_b[qaid_col_b].head(10).items():
            print(f"  [{idx}] '{value}' (타입: {type(value)})")

        # 각 키워드에 대한 필터 마스크 생성
        mask_a = pd.Series([False] * len(df_a))
        mask_b = pd.Series([False] * len(df_b))

        for keyword in keywords:
            print(f"  🔎 키워드 '{keyword}' 검색 중...")

            # 정규식 특수문자 이스케이프 처리
            import re
            escaped_keyword = re.escape(keyword)

            # 여러 방식으로 매칭 시도
            temp_mask_a = (
                df_a[qaid_col_a].str.contains(escaped_keyword, case=False, na=False, regex=True) |
                df_a[qaid_col_a].str.contains(keyword, case=False, na=False, regex=False) |
                df_a[qaid_col_a].str.lower().str.contains(keyword.lower(), na=False)
            )
            temp_mask_b = (
                df_b[qaid_col_b].str.contains(escaped_keyword, case=False, na=False, regex=True) |
                df_b[qaid_col_b].str.contains(keyword, case=False, na=False, regex=False) |
                df_b[qaid_col_b].str.lower().str.contains(keyword.lower(), na=False)
            )

            mask_a = mask_a | temp_mask_a
            mask_b = mask_b | temp_mask_b

            print(f"    - 파일 A에서 '{keyword}' 매칭: {temp_mask_a.sum()}개")
            print(f"    - 파일 B에서 '{keyword}' 매칭: {temp_mask_b.sum()}개")

        # 필터링된 데이터프레임 생성
        df_a_filtered = df_a[mask_a]
        df_b_filtered = df_b[mask_b]

        # 필터링 결과 출력
        print(f"  - 키워드 포함 파일 A 행: {len(df_a_filtered):,}개")
        for idx, row in df_a_filtered.head(5).iterrows():
            print(f"    * {row[qaid_col_a]}")
        if len(df_a_filtered) > 5:
            print(f"    * ... 외 {len(df_a_filtered) - 5}개")

        print(f"  - 키워드 포함 파일 B 행: {len(df_b_filtered):,}개")
        for idx, row in df_b_filtered.head(5).iterrows():
            print(f"    * {row[qaid_col_b]}")
        if len(df_b_filtered) > 5:
            print(f"    * ... 외 {len(df_b_filtered) - 5}개")

        # 구성품 기준으로 매칭
        merged_df = pd.merge(df_a_filtered, df_b_filtered,
                             left_on=qaid_col_a,
                             right_on=qaid_col_b,
                             how='inner')  # inner join으로 일치하는 것만 추출
    else:
        # 정확히 일치하는 문자열로 매칭
        print("🔍 정확히 일치하는 문자열로 매칭합니다.")

        # 구성품 기준으로 정확히 일치하는 데이터만 매칭
        merged_df = pd.merge(df_a, df_b,
                             left_on=qaid_col_a,
                             right_on=qaid_col_b,
                             how='inner')  # inner join으로 정확히 일치하는 것만 추출

    # 결과 저장
    merged_df.to_excel(result_file, index=False)

    # 결과 출력
    print(f"\n✅ 매칭 완료! 결과 파일 저장됨: {result_file}")
    print(f"📊 처리 결과:")
    print(f"  - 파일 A 행 수: {len(df_a):,}개")
    print(f"  - 파일 B 행 수: {len(df_b):,}개")
    if keywords:
        print(f"  - 키워드 {keywords} 포함 파일 A 행 수: {len(df_a_filtered):,}개")
        print(f"  - 키워드 {keywords} 포함 파일 B 행 수: {len(df_b_filtered):,}개")
    print(f"  - 매칭된 행 수: {len(merged_df):,}개")

    # 매칭된 결과 샘플 출력
    if len(merged_df) > 0:
        print("\n📋 매칭된 결과 샘플:")
        for idx, row in merged_df.head(5).iterrows():
            print(f"  * {row[qaid_col_a if qaid_col_a in merged_df.columns else qaid_col_a.replace('_x', '')]}")
        if len(merged_df) > 5:
            print(f"  * ... 외 {len(merged_df) - 5}개")


# 함수 실행
if __name__ == "__main__":
    match_by_qaid(
        file_a="A.xlsx",  # 기준 파일
        file_b="B.xlsx",  # 매칭할 파일
        result_file="matched_result.xlsx",  # 결과 파일
        sheet_a="Sheet1",
        sheet_b="Sheet1",
        qaid_col_a="구성품",
        qaid_col_b="구성품",
        header_row=1,  # 엑셀에서 두 번째 행이 헤더인 경우
        keywords=None  # None으로 설정하면 정확히 일치하는 문자열로 매칭
    )
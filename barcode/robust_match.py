import pandas as pd
import re
import unicodedata
import numpy as np


def robust_match(file_a, file_b, result_file,
                sheet_a='Sheet1', sheet_b='Sheet1',
                header_row=1):
    """
    데이터 형식과 인코딩 문제를 해결하는 강화된 매칭 함수
    """
    print("📂 파일 읽기 중... (인코딩 문제 해결)")
    
    # 다양한 인코딩으로 파일 읽기 시도
    def read_excel_robust(file_path, sheet_name, header_row):
        encodings = ['utf-8', 'cp949', 'euc-kr', 'utf-8-sig']
        
        for encoding in encodings:
            try:
                if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                    df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row, engine='openpyxl')
                else:
                    df = pd.read_csv(file_path, encoding=encoding, header=header_row)
                print(f"  - {file_path}: {encoding} 인코딩으로 성공")
                return df
            except Exception as e:
                print(f"  - {file_path}: {encoding} 인코딩 실패 - {str(e)[:50]}...")
                continue
        
        raise Exception(f"{file_path} 파일을 읽을 수 없습니다.")
    
    # 파일 읽기
    df_a = read_excel_robust(file_a, sheet_a, header_row)
    df_b = read_excel_robust(file_b, sheet_b, header_row)
    
    print(f"  - {file_a}: {len(df_a):,}행 읽기 완료")
    print(f"  - {file_b}: {len(df_b):,}행 읽기 완료")

    # 컬럼명 정리 및 출력
    def clean_column_names(df, file_name):
        print(f"\n📋 {file_name} 원본 컬럼명:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. '{col}' (타입: {type(col)})")
        
        # 컬럼명 정리
        df.columns = [str(col).strip() for col in df.columns]
        return df
    
    df_a = clean_column_names(df_a, "파일 A")
    df_b = clean_column_names(df_b, "파일 B")

    # 구성품 컬럼 찾기
    def find_component_column(df, file_name):
        possible_names = ['구성품', '제품명', '상품명', '품목', 'component', 'product', 'item']

        for col in df.columns:
            col_clean = str(col).strip().lower()
            for name in possible_names:
                if name in col_clean:
                    print(f"  - {file_name} 구성품 컬럼 발견: '{col}'")
                    return col

        # 직접 선택
        print(f"\n❓ {file_name}에서 구성품 컬럼을 찾을 수 없습니다.")
        print("사용 가능한 컬럼:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. '{col}'")

        # 첫 번째 컬럼을 기본값으로 사용
        return df.columns[0] if len(df.columns) > 0 else None

    # bar_cd 컬럼 찾기
    def find_barcode_column(df, file_name):
        possible_names = ['bar_cd', 'barcode', '바코드', 'bar_code', 'bar코드']

        for col in df.columns:
            col_clean = str(col).strip().lower()
            for name in possible_names:
                if name in col_clean:
                    print(f"  - {file_name} bar_cd 컬럼 발견: '{col}'")
                    return col

        print(f"  - {file_name}에서 bar_cd 컬럼을 찾을 수 없습니다.")
        return None

    qaid_col_a = find_component_column(df_a, "파일 A")
    qaid_col_b = find_component_column(df_b, "파일 B")
    barcode_col_a = find_barcode_column(df_a, "파일 A")
    barcode_col_b = find_barcode_column(df_b, "파일 B")

    if not qaid_col_a or not qaid_col_b:
        raise Exception("구성품 컬럼을 찾을 수 없습니다.")

    print(f"\n📋 사용할 컬럼:")
    print(f"  - A파일 구성품: '{qaid_col_a}'")
    print(f"  - B파일 구성품: '{qaid_col_b}'")
    print(f"  - A파일 bar_cd: '{barcode_col_a}'" if barcode_col_a else "  - A파일 bar_cd: 없음")
    print(f"  - B파일 bar_cd: '{barcode_col_b}'" if barcode_col_b else "  - B파일 bar_cd: 없음")

    # 텍스트 정규화 함수
    def normalize_text(text):
        """텍스트 정규화 - 인코딩, 공백, 특수문자 문제 해결"""
        if pd.isna(text) or text is None:
            return ""
        
        # 문자열로 변환
        text = str(text)
        
        # Unicode 정규화 (NFD -> NFC)
        text = unicodedata.normalize('NFC', text)
        
        # 다양한 공백 문자를 일반 공백으로 변환
        text = re.sub(r'[\u00A0\u1680\u2000-\u200B\u202F\u205F\u3000\uFEFF]', ' ', text)
        
        # 연속된 공백을 하나로
        text = re.sub(r'\s+', ' ', text)
        
        # 앞뒤 공백 제거
        text = text.strip()
        
        # 특수문자 정리 (선택적)
        # text = re.sub(r'[^\w\s가-힣]', '', text)
        
        return text

    # 데이터 정규화
    print(f"\n🔧 데이터 정규화 중...")
    
    df_a[qaid_col_a] = df_a[qaid_col_a].apply(normalize_text)
    df_b[qaid_col_b] = df_b[qaid_col_b].apply(normalize_text)
    
    # 빈 값 제거
    df_a_clean = df_a[df_a[qaid_col_a] != ''].copy()
    df_b_clean = df_b[df_b[qaid_col_b] != ''].copy()
    
    print(f"  - 파일 A 유효한 값: {len(df_a_clean):,}개")
    print(f"  - 파일 B 유효한 값: {len(df_b_clean):,}개")

    # 정규화된 데이터 샘플 출력
    print(f"\n📋 정규화된 파일 A 구성품 샘플:")
    for i, value in enumerate(df_a_clean[qaid_col_a].head(5)):
        print(f"  {i+1}. '{value}' (길이: {len(value)})")
    
    print(f"\n📋 정규화된 파일 B 구성품 샘플:")
    for i, value in enumerate(df_b_clean[qaid_col_b].head(5)):
        print(f"  {i+1}. '{value}' (길이: {len(value)})")

    # 고급 매칭 함수들
    def similarity_score(text1, text2):
        """두 텍스트 간의 유사도 계산"""
        if not text1 or not text2:
            return 0
        
        text1 = text1.lower()
        text2 = text2.lower()
        
        # 정확 일치
        if text1 == text2:
            return 1.0
        
        # 포함 관계
        if text1 in text2 or text2 in text1:
            return 0.8
        
        # 단어 기반 유사도
        words1 = set(re.findall(r'[가-힣a-zA-Z0-9]+', text1))
        words2 = set(re.findall(r'[가-힣a-zA-Z0-9]+', text2))
        
        if words1 and words2:
            intersection = len(words1 & words2)
            union = len(words1 | words2)
            jaccard = intersection / union if union > 0 else 0
            
            if jaccard > 0.3:
                return jaccard
        
        # 문자 기반 유사도
        chars1 = set(text1)
        chars2 = set(text2)
        
        if chars1 and chars2:
            char_intersection = len(chars1 & chars2)
            char_union = len(chars1 | chars2)
            char_jaccard = char_intersection / char_union if char_union > 0 else 0
            
            if char_jaccard > 0.5:
                return char_jaccard * 0.6
        
        return 0

    # 매칭 수행
    print(f"\n🔍 매칭 수행 중...")
    matching_results = []
    
    total_comparisons = len(df_a_clean) * len(df_b_clean)
    comparison_count = 0
    
    for idx_a, row_a in df_a_clean.iterrows():
        a_component = row_a[qaid_col_a]
        
        best_match = None
        best_score = 0
        
        for idx_b, row_b in df_b_clean.iterrows():
            b_component = row_b[qaid_col_b]
            comparison_count += 1
            
            # 진행률 출력
            if comparison_count % 5000 == 0:
                progress = (comparison_count / total_comparisons) * 100
                print(f"    진행률: {progress:.1f}%")
            
            score = similarity_score(a_component, b_component)
            
            if score > 0.3 and score > best_score:  # 30% 이상 유사도
                best_score = score
                best_match = {
                    'b_component': b_component,
                    'b_row': row_b,
                    'score': score
                }
        
        # 최고 매칭 결과 저장
        if best_match:
            result_row = row_a.to_dict()
            result_row[f'B파일_{qaid_col_b}'] = best_match['b_component']
            result_row['매칭여부'] = '매칭됨'
            result_row['유사도점수'] = round(best_match['score'], 3)
            result_row['A파일_원본'] = a_component
            result_row['B파일_원본'] = best_match['b_component']

            # bar_cd 정보 추가
            if barcode_col_a:
                result_row['A파일_bar_cd'] = row_a.get(barcode_col_a, 'N/A')
            else:
                result_row['A파일_bar_cd'] = 'N/A'

            if barcode_col_b:
                result_row['B파일_bar_cd'] = best_match['b_row'].get(barcode_col_b, 'N/A')
            else:
                result_row['B파일_bar_cd'] = 'N/A'

            # 매칭 타입 결정
            if best_match['score'] >= 0.9:
                result_row['매칭타입'] = '높은유사도'
            elif best_match['score'] >= 0.7:
                result_row['매칭타입'] = '중간유사도'
            else:
                result_row['매칭타입'] = '낮은유사도'

            matching_results.append(result_row)

    print(f"\n📊 매칭 결과:")
    print(f"  - 총 매칭된 결과: {len(matching_results):,}개")
    
    if len(matching_results) > 0:
        # 결과 데이터프레임 생성
        matching_df = pd.DataFrame(matching_results)
        
        # 매칭 타입별 통계
        match_stats = matching_df['매칭타입'].value_counts()
        print(f"\n📊 매칭 타입별 통계:")
        for match_type, count in match_stats.items():
            print(f"  - {match_type}: {count:,}개")
        
        # 매칭되지 않은 A 파일 행들
        matched_a_components = set([result[qaid_col_a] for result in matching_results])
        unmatched_rows = df_a_clean[~df_a_clean[qaid_col_a].isin(matched_a_components)].copy()
        
        # 결과 저장
        with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
            # 매칭 결과
            matching_df.to_excel(writer, sheet_name='매칭결과', index=False)
            
            # 매칭되지 않은 행들
            if len(unmatched_rows) > 0:
                unmatched_rows.to_excel(writer, sheet_name='매칭안된행', index=False)
            
            # 통계
            stats_df = pd.DataFrame({
                '매칭타입': match_stats.index,
                '개수': match_stats.values
            })
            stats_df.to_excel(writer, sheet_name='매칭통계', index=False)
            
            # 원본 데이터 (정규화 전후 비교용)
            df_a.head(100).to_excel(writer, sheet_name='A파일원본샘플', index=False)
            df_b.head(100).to_excel(writer, sheet_name='B파일원본샘플', index=False)

        print(f"\n✅ 결과 파일 저장 완료: {result_file}")
        
        # 매칭 결과 샘플 출력
        print(f"\n📋 매칭 결과 샘플:")
        for i, result in enumerate(matching_results[:5]):
            print(f"  {i+1}. A: '{result['A파일_원본'][:30]}...'")
            print(f"     B: '{result['B파일_원본'][:30]}...'")
            print(f"     A_bar_cd: {result['A파일_bar_cd']}, B_bar_cd: {result['B파일_bar_cd']}")
            print(f"     타입: {result['매칭타입']}, 점수: {result['유사도점수']}")
            
    else:
        print("\n❌ 매칭된 결과가 없습니다.")
        
        # 진단 정보 저장
        diagnosis_df = pd.DataFrame({
            '진단항목': ['A파일 총 행수', 'B파일 총 행수', 'A파일 유효행수', 'B파일 유효행수', 
                       'A파일 구성품 컬럼', 'B파일 구성품 컬럼'],
            '값': [len(df_a), len(df_b), len(df_a_clean), len(df_b_clean), 
                  qaid_col_a, qaid_col_b]
        })
        
        with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
            diagnosis_df.to_excel(writer, sheet_name='진단정보', index=False)
            df_a_clean.head(50).to_excel(writer, sheet_name='A파일정규화샘플', index=False)
            df_b_clean.head(50).to_excel(writer, sheet_name='B파일정규화샘플', index=False)
        
        print(f"📄 진단 정보 저장: {result_file}")


# 함수 실행
if __name__ == "__main__":
    robust_match(
        file_a="A.xlsx",
        file_b="B.xlsx", 
        result_file="robust_matched_result.xlsx",
        sheet_a="Sheet1",
        sheet_b="Sheet1",
        header_row=1
    )

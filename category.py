import pandas as pd
import os
from datetime import datetime

# 파일 경로
price_file = "price.xlsx"
ref_file = "1.xlsx"
result_file = "category_result.xlsx"

# 파일 읽기
try:
    price_df = pd.read_excel(price_file, sheet_name="데이터유효성_일반", header=2, usecols=["4차", "5차", "상품단가구간", "리퍼완료", "점검완료(중)"])
except FileNotFoundError:
    print(f"오류: '{price_file}' 파일을 찾을 수 없습니다.")
    exit()
except KeyError as e:
    print(f"오류: '{price_file}' 시트 또는 컬럼 '{e}'을 찾을 수 없습니다.")
    exit()

try:
    ref_df = pd.read_excel(ref_file, sheet_name="Sheet1", header=1, usecols=["QAID", "4차", "5차", "상품단가구간", "수리상태", "수리비용"])
except FileNotFoundError:
    print(f"오류: '{ref_file}' 파일을 찾을 수 없습니다.")
    exit()
except KeyError as e:
    print(f"오류: '{ref_file}' 시트 또는 컬럼 '{e}'을 찾을 수 없습니다.")
    exit()

# 컬럼 정리
price_df.columns = price_df.columns.str.strip()
ref_df.columns = ref_df.columns.str.strip()

# 데이터 전처리
for col in ["4차", "상품단가구간"]:
    price_df[col] = price_df[col].astype(str).str.strip()
    ref_df[col] = ref_df[col].astype(str).str.strip()

price_df["5차"] = price_df["5차"].astype(str).str.strip()
ref_df["5차"] = ref_df["5차"].astype(str).str.strip()

price_df["리퍼완료"] = pd.to_numeric(price_df["리퍼완료"], errors='coerce')
price_df["점검완료(중)"] = pd.to_numeric(price_df["점검완료(중)"], errors='coerce')
ref_df["수리비용"] = pd.to_numeric(ref_df["수리비용"], errors='coerce')

# 결과 컬럼 초기화
ref_df["검증결과"] = None
ref_df["검수가격"] = None
ref_df["매칭가격"] = None

# 검증 처리
xl_products = ["TV", "모니터"]

for idx, row in ref_df.iterrows():
    p4 = row["4차"]
    p5 = row["5차"]
    price_range = row["상품단가구간"]
    status = row["수리상태"]
    cost = row["수리비용"]

    # 1. 4차, 5차 모두 일치하는 price 행 추출
    candidates = price_df[
        (price_df["4차"] == p4) &
        (price_df["5차"] == p5)
    ]

    # 2. 상품단가구간까지 모두 일치하는 행 우선 매칭
    matched_rows = candidates[candidates["상품단가구간"] == price_range]

    # 3. 없으면 "공통" 상품단가구간으로 재매칭
    if matched_rows.empty:
        matched_rows = candidates[candidates["상품단가구간"] == "공통"]

    # 4. 그래도 없으면 XL 예외처리
    if candidates.empty:
        # 4차, 5차 자체가 없음
        if p5 in xl_products and price_range == "XL":
            ref_df.at[idx, "검증결과"] = "XL"
        else:
            ref_df.at[idx, "검증결과"] = "오류: 기준 정보 없음"
        continue

    if matched_rows.empty:
        # 4차, 5차는 있으나 상품단가구간이 모두 없음
        if p5 in xl_products and price_range == "XL":
            ref_df.at[idx, "검증결과"] = "XL"
        else:
            ref_df.at[idx, "검증결과"] = "오류: 기준가격 없음"
        continue

    matched = False
    for _, mrow in matched_rows.iterrows():
        if status == "리퍼완료":
            standard_price = mrow["리퍼완료"]
            matched_cost = mrow["리퍼완료"]
        elif status == "점검완료(중)":
            standard_price = mrow["점검완료(중)"]
            matched_cost = mrow["점검완료(중)"]
        else:
            standard_price = None
            matched_cost = None

        if pd.isna(standard_price):
            continue

        ref_df.at[idx, "매칭가격"] = standard_price
        ref_df.at[idx, "검수가격"] = matched_cost

        if standard_price == cost:
            ref_df.at[idx, "검증결과"] = "정상"
            matched = True
            break

    if not matched:
        if p5 in xl_products and price_range == "XL":
            ref_df.at[idx, "검증결과"] = "XL"
        elif pd.isna(ref_df.at[idx, "매칭가격"]):
            ref_df.at[idx, "검증결과"] = "오류: 기준가격 없음"
        else:
            ref_df.at[idx, "검증결과"] = "불일치"

# 결과 저장
output_columns = ["QAID", "4차", "5차", "상품단가구간", "수리상태", "수리비용", "검증결과", "검수가격", "매칭가격"]

if os.path.exists(result_file):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_result_file = f"category_result_{timestamp}.xlsx"
    ref_df.to_excel(new_result_file, columns=output_columns, index=False)
    print(f"경고: '{result_file}'이 이미 존재하여 '{new_result_file}'로 저장했습니다.")
else:
    ref_df.to_excel(result_file, columns=output_columns, index=False)
    print(f"검증 완료! 결과 파일 저장: {result_file}")

print(f"처리된 행 수: {len(ref_df)}개")

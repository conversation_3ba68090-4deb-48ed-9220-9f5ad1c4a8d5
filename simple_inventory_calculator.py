import pandas as pd
import numpy as np


def simple_inventory_calculator(file_path, result_file,
                               sheet_name=None, header_row=0):
    """
    간단한 재고 사용량 계산기
    
    계산식:
    1. 총재고 = 이월재고 + 당월입고
    2. 1주사용량 = 총재고 - 1주재고
    3. 2주사용량 = 1주재고 - 2주재고
    4. 3주사용량 = 2주재고 - 3주재고
    5. 4주사용량 = 3주재고 - 4주재고
    6. 총사용량 = 1주사용량 + 2주사용량 + 3주사용량 + 4주사용량
    7. 사용률 = (총사용량 / 총재고) × 100
    """
    print("📂 파일 읽기...")
    
    # 파일 및 시트 읽기
    try:
        excel_file = pd.ExcelFile(file_path)
        if sheet_name is None or sheet_name not in excel_file.sheet_names:
            sheet_name = excel_file.sheet_names[0]
            print(f"  - 첫 번째 시트 '{sheet_name}' 사용")
        
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        print(f"  - 데이터 읽기 완료: {len(df):,}행")
        
    except Exception as e:
        print(f"❌ 파일 읽기 실패: {e}")
        return
    
    # 컬럼 자동 찾기
    print(f"\n🔍 컬럼 찾기...")
    
    def find_column(keywords, columns):
        for col in columns:
            col_str = str(col).strip().lower()
            if any(keyword in col_str for keyword in keywords):
                return col
        return None
    
    # 필요한 컬럼 찾기
    columns = {
        '이월재고': find_column(['이월재고', '이월', '기초재고'], df.columns),
        '당월입고': find_column(['당월입고', '입고', '입고량'], df.columns),
        '1주': find_column(['1주'], df.columns),
        '2주': find_column(['2주'], df.columns),
        '3주': find_column(['3주'], df.columns),
        '4주': find_column(['4주'], df.columns)
    }
    
    # 컬럼 확인
    missing = [k for k, v in columns.items() if v is None]
    if missing:
        print(f"❌ 다음 컬럼을 찾을 수 없습니다: {missing}")
        print(f"📋 사용 가능한 컬럼: {list(df.columns)}")
        return
    
    print(f"✅ 모든 필요 컬럼 발견:")
    for name, col in columns.items():
        print(f"  - {name}: '{col}'")
    
    # 데이터 전처리
    print(f"\n🔧 데이터 전처리...")
    
    # 숫자형 변환 및 결측값 0으로 처리
    for col in columns.values():
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
    
    # 계산 수행
    print(f"\n📊 계산 수행...")
    
    # 1. 총재고 = 이월재고 + 당월입고
    df['총재고'] = df[columns['이월재고']] + df[columns['당월입고']]
    
    # 2. 주차별 사용량 계산
    df['1주사용량'] = df['총재고'] - df[columns['1주']]
    df['2주사용량'] = df[columns['1주']] - df[columns['2주']]
    df['3주사용량'] = df[columns['2주']] - df[columns['3주']]
    df['4주사용량'] = df[columns['3주']] - df[columns['4주']]
    
    # 3. 총사용량 = 모든 주차 사용량 합계
    df['총사용량'] = df['1주사용량'] + df['2주사용량'] + df['3주사용량'] + df['4주사용량']
    
    # 4. 사용률 = (총사용량 / 총재고) × 100
    df['사용률'] = np.where(df['총재고'] > 0, (df['총사용량'] / df['총재고']) * 100, 0)
    
    # 5. 잔여재고 = 4주차 재고
    df['잔여재고'] = df[columns['4주']]
    
    # 결과 정리
    print(f"\n📈 계산 완료!")
    print(f"  - 처리된 행수: {len(df):,}개")
    print(f"  - 총재고 합계: {df['총재고'].sum():,.0f}")
    print(f"  - 총사용량 합계: {df['총사용량'].sum():,.0f}")
    print(f"  - 평균 사용률: {df['사용률'].mean():.1f}%")
    
    # 계산식 표시
    print(f"\n📋 적용된 계산식:")
    print(f"  1. 총재고 = 이월재고 + 당월입고")
    print(f"  2. 1주사용량 = 총재고 - 1주재고")
    print(f"  3. 2주사용량 = 1주재고 - 2주재고")
    print(f"  4. 3주사용량 = 2주재고 - 3주재고")
    print(f"  5. 4주사용량 = 3주재고 - 4주재고")
    print(f"  6. 총사용량 = 1주사용량 + 2주사용량 + 3주사용량 + 4주사용량")
    print(f"  7. 사용률 = (총사용량 / 총재고) × 100")
    print(f"  8. 잔여재고 = 4주재고")
    
    # 결과 컬럼 정리
    result_columns = [
        # 원본 데이터
        columns['이월재고'], columns['당월입고'],
        columns['1주'], columns['2주'], columns['3주'], columns['4주'],
        # 계산 결과
        '총재고', '1주사용량', '2주사용량', '3주사용량', '4주사용량',
        '총사용량', '사용률', '잔여재고'
    ]
    
    # 다른 컬럼들도 포함 (품목명, 코드 등)
    other_columns = [col for col in df.columns if col not in result_columns]
    final_columns = other_columns + result_columns
    
    result_df = df[final_columns].copy()
    
    # 요약 통계
    summary = pd.DataFrame({
        '항목': ['이월재고', '당월입고', '총재고', '1주사용량', '2주사용량', 
                '3주사용량', '4주사용량', '총사용량', '잔여재고', '사용률(%)'],
        '합계': [
            df[columns['이월재고']].sum(),
            df[columns['당월입고']].sum(),
            df['총재고'].sum(),
            df['1주사용량'].sum(),
            df['2주사용량'].sum(),
            df['3주사용량'].sum(),
            df['4주사용량'].sum(),
            df['총사용량'].sum(),
            df['잔여재고'].sum(),
            df['사용률'].mean()
        ],
        '평균': [
            df[columns['이월재고']].mean(),
            df[columns['당월입고']].mean(),
            df['총재고'].mean(),
            df['1주사용량'].mean(),
            df['2주사용량'].mean(),
            df['3주사용량'].mean(),
            df['4주사용량'].mean(),
            df['총사용량'].mean(),
            df['잔여재고'].mean(),
            df['사용률'].mean()
        ]
    })
    
    # 계산식 설명
    formula_df = pd.DataFrame({
        '계산항목': ['총재고', '1주사용량', '2주사용량', '3주사용량', '4주사용량', 
                   '총사용량', '사용률', '잔여재고'],
        '계산식': [
            '이월재고 + 당월입고',
            '총재고 - 1주재고',
            '1주재고 - 2주재고',
            '2주재고 - 3주재고',
            '3주재고 - 4주재고',
            '1주사용량 + 2주사용량 + 3주사용량 + 4주사용량',
            '(총사용량 / 총재고) × 100',
            '4주재고'
        ],
        '설명': [
            '기초재고와 입고량의 합계',
            '첫 주차에 사용된 수량',
            '두 번째 주차에 사용된 수량',
            '세 번째 주차에 사용된 수량',
            '네 번째 주차에 사용된 수량',
            '전체 기간 동안 사용된 총 수량',
            '총재고 대비 사용된 비율(%)',
            '마지막 주차 남은 재고'
        ]
    })
    
    # 엑셀 저장
    print(f"\n💾 결과 저장 중...")
    
    with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
        # 메인 결과
        result_df.to_excel(writer, sheet_name='계산결과', index=False)
        
        # 요약 통계
        summary.to_excel(writer, sheet_name='요약통계', index=False)
        
        # 계산식 설명
        formula_df.to_excel(writer, sheet_name='계산식설명', index=False)
        
        # 원본 데이터
        df_original = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        df_original.to_excel(writer, sheet_name='원본데이터', index=False)
    
    print(f"✅ 저장 완료: {result_file}")
    print(f"📋 저장된 시트:")
    print(f"  - '계산결과': 모든 계산 결과 ({len(result_df):,}행)")
    print(f"  - '요약통계': 항목별 합계/평균")
    print(f"  - '계산식설명': 사용된 계산식과 설명")
    print(f"  - '원본데이터': 원본 데이터")
    
    # 샘플 결과 출력
    print(f"\n📋 계산 결과 샘플 (상위 3개):")
    sample_cols = ['총재고', '1주사용량', '2주사용량', '3주사용량', '4주사용량', 
                   '총사용량', '사용률', '잔여재고']
    
    for i, row in result_df.head(3).iterrows():
        print(f"\n  {i+1}번째 항목:")
        for col in sample_cols:
            if col == '사용률':
                print(f"    {col}: {row[col]:.1f}%")
            else:
                print(f"    {col}: {row[col]:,.0f}")
    
    print(f"\n📊 주차별 사용량 통계:")
    for week in ['1주', '2주', '3주', '4주']:
        usage_col = f'{week}사용량'
        total = df[usage_col].sum()
        avg = df[usage_col].mean()
        print(f"  - {week}차: 총 {total:,.0f}, 평균 {avg:.1f}")
    
    return result_df


# 함수 실행
if __name__ == "__main__":
    simple_inventory_calculator(
        file_path="inventory.xlsx",  # 실제 파일명으로 변경
        result_file="simple_inventory_result.xlsx",  # 결과 파일
        sheet_name=None,  # 자동 탐지
        header_row=0  # 헤더 행 번호
    )

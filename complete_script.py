import pandas as pd

# 파일 경로 지정
price_file = "price.xlsx"
ref_file = "1.xlsx"

# 엑셀 파일 읽기
price_df = pd.read_excel(price_file, sheet_name="데이터유효성_일반", header=2, usecols=["4차", "5차", "상품단가구간", "리퍼완료", "점검완료(중)"])
ref_df = pd.read_excel(ref_file, sheet_name="Sheet1", header=1)

# 컬럼명 정리 (공백 제거)
price_df.columns = price_df.columns.str.strip()
ref_df.columns = ref_df.columns.str.strip()

# 매칭 및 비교에 사용할 컬럼 명시
price_product_column = "4차"
price_repair_column = "5차"
price_repair_check_column = "리퍼완료"
price_normal_check_column = "점검완료(중)"
ref_product_column_4 = "4차"
ref_product_column_5 = "5차"
ref_status_column = "수리상태"
ref_cost_column = "수리비용"

# 컬럼 존재 여부 확인
required_columns_price = [price_product_column, price_repair_column, price_repair_check_column, price_normal_check_column]
required_columns_ref = [ref_product_column_4, ref_product_column_5, ref_status_column, ref_cost_column]

for col in required_columns_price:
    if col not in price_df.columns:
        raise KeyError(f"'{col}' 컬럼이 price.xlsx에 존재하지 않습니다.")
for col in required_columns_ref:
    if col not in ref_df.columns:
        raise KeyError(f"'{col}' 컬럼이 1.xlsx에 존재하지 않습니다.")

# 공백 제거 및 데이터 변환
price_df[price_product_column] = price_df[price_product_column].astype(str).str.strip()
price_df[price_repair_column] = pd.to_numeric(price_df[price_repair_column], errors='coerce')
price_df[price_repair_check_column] = pd.to_numeric(price_df[price_repair_check_column], errors='coerce')
price_df[price_normal_check_column] = pd.to_numeric(price_df[price_normal_check_column], errors='coerce')

ref_df[ref_product_column_4] = ref_df[ref_product_column_4].astype(str).str.strip()
ref_df[ref_product_column_5] = pd.to_numeric(ref_df[ref_product_column_5], errors='coerce')
ref_df[ref_cost_column] = pd.to_numeric(ref_df[ref_cost_column], errors='coerce')

# 검증 결과를 저장할 빈 컬럼 추가
ref_df['검증결과'] = None
ref_df['검수가격'] = None

# price_df에서 '4차'와 '5차' 컬럼을 기준으로 멀티 인덱스 생성 및 정렬
price_df_indexed = price_df.set_index([price_product_column, price_repair_column]).sort_index()

# 건별로 검증 및 결과 저장
for idx, row in ref_df.iterrows():
    product_4 = row[ref_product_column_4]
    product_5 = row[ref_product_column_5]
    repair_status = row[ref_status_column]
    repair_cost = row[ref_cost_column]

    try:
        matching_price = price_df_indexed.loc[(product_4, product_5)]
    except KeyError:
        ref_df.at[idx, '검증결과'] = "오류: 불일치 (price.xlsx에 없음)"
        ref_df.at[idx, '검수가격'] = None
        continue

    if isinstance(matching_price, pd.Series):
        matching_price = pd.DataFrame([matching_price])

    if repair_status == "리퍼완료":
        price_values = matching_price[price_repair_check_column].dropna()
        if not price_values.empty and any(float(repair_cost) == float(v) for v in price_values):
            ref_df.at[idx, '검증결과'] = "정상"
            ref_df.at[idx, '검수가격'] = int(repair_cost)
        elif price_values.empty:
            ref_df.at[idx, '검증결과'] = "오류: 불일치 (리퍼완료 가격 NaN)"
            ref_df.at[idx, '검수가격'] = "NaN"
        else:
            기준값 = ', '.join(map(str, price_values.unique()))
            ref_df.at[idx, '검증결과'] = f"오류: 불일치 (수리비용 값 다름, 기준:{기준값})"
            ref_df.at[idx, '검수가격'] = int(repair_cost) if pd.notnull(repair_cost) else "NaN"

    elif repair_status == "점검완료(중)":
        price_values = matching_price[price_normal_check_column].dropna()
        if not price_values.empty and any(float(repair_cost) == float(v) for v in price_values):
            ref_df.at[idx, '검증결과'] = "정상"
            ref_df.at[idx, '검수가격'] = int(repair_cost)
        elif price_values.empty:
            ref_df.at[idx, '검증결과'] = "오류: 카테고리 미포함"
            ref_df.at[idx, '검수가격'] = "none"
        else:
            기준값 = ', '.join(map(str, price_values.unique()))
            ref_df.at[idx, '검증결과'] = f"오류: 불일치 (수리비용 값 다름, 기준:{기준값})"
            ref_df.at[idx, '검수가격'] = int(repair_cost) if pd.notnull(repair_cost) else "NaN"

# 수리비와 검수가격 비교하여 유효성검증 컬럼 추가
ref_df['유효성검증'] = ref_df.apply(lambda x: 'TRUE' if pd.notnull(x[ref_cost_column]) and pd.notnull(x['검수가격']) and float(x[ref_cost_column]) == float(x['검수가격']) else 'FALSE', axis=1)

# 결과 저장
result_file = "category_result.xlsx"
ref_df.to_excel(result_file, index=False)

print(f"검증 완료! 결과 파일 저장: {result_file}")
print(f"처리된 행 수: {len(ref_df)}개")


def match_by_qaid(file_a, file_b, result_file,
                  sheet_a='Sheet1', sheet_b='Sheet1',
                  qaid_col_a='구성품', qaid_col_b='구성품',
                  header_row=1, keywords=None):
    """
    파일 A를 기준으로 파일 B에서 QAID가 일치하는 데이터만 추출하여 새 파일 생성

    Parameters:
    - file_a: 기준이 되는 파일 경로
    - file_b: 매칭할 대상 파일 경로
    - result_file: 결과 저장 파일 경로
    - sheet_a, sheet_b: 각 파일의 시트명
    - qaid_col_a, qaid_col_b: 각 파일의 QAID 컬럼명
    - header_row: 엑셀 헤더 행 번호 (0부터 시작)
    - keywords: 구성품에서 검색할 특정 단어 리스트 (None이면 정확히 일치하는 문자열로 매칭)
    """
    # 파일 읽기
    df_a = pd.read_excel(file_a, sheet_name=sheet_a, header=header_row)
    df_b = pd.read_excel(file_b, sheet_name=sheet_b, header=header_row)

    # 컬럼 존재 여부 확인
    if qaid_col_a not in df_a.columns:
        raise KeyError(f"파일 A에 '{qaid_col_a}' 컬럼이 없습니다. 사용 가능한 컬럼: {list(df_a.columns)}")
    if qaid_col_b not in df_b.columns:
        raise KeyError(f"파일 B에 '{qaid_col_b}' 컬럼이 없습니다. 사용 가능한 컬럼: {list(df_b.columns)}")

    # 구성품 컬럼 전처리
    df_a[qaid_col_a] = df_a[qaid_col_a].astype(str).str.strip()
    df_b[qaid_col_b] = df_b[qaid_col_b].astype(str).str.strip()

    # 키워드 매칭 방식 사용
    if keywords:
        # 단일 키워드를 리스트로 변환
        if isinstance(keywords, str):
            keywords = [keywords]

        print(f"🔍 키워드 검색: {keywords}")

        # 각 키워드에 대한 필터 마스크 생성
        mask_a = df_a[qaid_col_a].str.contains(keywords[0], case=False)
        mask_b = df_b[qaid_col_b].str.contains(keywords[0], case=False)

        # 추가 키워드에 대한 OR 조건 적용
        for keyword in keywords[1:]:
            mask_a = mask_a | df_a[qaid_col_a].str.contains(keyword, case=False)
            mask_b = mask_b | df_b[qaid_col_b].str.contains(keyword, case=False)

        # 필터링된 데이터프레임 생성
        df_a_filtered = df_a[mask_a]
        df_b_filtered = df_b[mask_b]

        # 필터링 결과 출력
        print(f"  - 키워드 포함 파일 A 행: {len(df_a_filtered):,}개")
        for idx, row in df_a_filtered.head(5).iterrows():
            print(f"    * {row[qaid_col_a]}")
        if len(df_a_filtered) > 5:
            print(f"    * ... 외 {len(df_a_filtered) - 5}개")

        print(f"  - 키워드 포함 파일 B 행: {len(df_b_filtered):,}개")
        for idx, row in df_b_filtered.head(5).iterrows():
            print(f"    * {row[qaid_col_b]}")
        if len(df_b_filtered) > 5:
            print(f"    * ... 외 {len(df_b_filtered) - 5}개")

        # 구성품 기준으로 매칭
        merged_df = pd.merge(df_a_filtered, df_b_filtered,
                             left_on=qaid_col_a,
                             right_on=qaid_col_b,
                             how='inner')  # inner join으로 일치하는 것만 추출
    else:
        # 정확히 일치하는 문자열로 매칭
        print("🔍 정확히 일치하는 문자열로 매칭합니다.")

        # 구성품 기준으로 정확히 일치하는 데이터만 매칭
        merged_df = pd.merge(df_a, df_b,
                             left_on=qaid_col_a,
                             right_on=qaid_col_b,
                             how='inner')  # inner join으로 정확히 일치하는 것만 추출

    # 결과 저장
    merged_df.to_excel(result_file, index=False)

    # 결과 출력
    print(f"\n✅ 매칭 완료! 결과 파일 저장됨: {result_file}")
    print(f"📊 처리 결과:")
    print(f"  - 파일 A 행 수: {len(df_a):,}개")
    print(f"  - 파일 B 행 수: {len(df_b):,}개")
    if keywords:
        print(f"  - 키워드 {keywords} 포함 파일 A 행 수: {len(df_a_filtered):,}개")
        print(f"  - 키워드 {keywords} 포함 파일 B 행 수: {len(df_b_filtered):,}개")
    print(f"  - 매칭된 행 수: {len(merged_df):,}개")

    # 매칭된 결과 샘플 출력
    if len(merged_df) > 0:
        print("\n📋 매칭된 결과 샘플:")
        for idx, row in merged_df.head(5).iterrows():
            print(f"  * {row[qaid_col_a if qaid_col_a in merged_df.columns else qaid_col_a.replace('_x', '')]}")
        if len(merged_df) > 5:
            print(f"  * ... 외 {len(merged_df) - 5}개")


# 함수 실행
if __name__ == "__main__":
    match_by_qaid(
        file_a="A.xlsx",  # 기준 파일
        file_b="B.xlsx",  # 매칭할 파일
        result_file="matched_result.xlsx",  # 결과 파일
        sheet_a="Sheet1",
        sheet_b="Sheet1",
        qaid_col_a="구성품",
        qaid_col_b="구성품",
        header_row=1,  # 엑셀에서 두 번째 행이 헤더인 경우
        keywords=None  # None으로 설정하면 정확히 일치하는 문자열로 매칭
    )

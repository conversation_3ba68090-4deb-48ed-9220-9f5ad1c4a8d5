import pandas as pd


def update_component_by_qaid(source_file, target_file, result_file,
                             source_sheet='Sheet1', target_sheet='Sheet1',
                             source_qaid_col='QAID', source_component_col='구성품',
                             target_qaid_col='QAID', target_component_col='구성품',
                             header_row=1):
    """
    source_file(contentslist)의 QAID 기준으로 target_file(2.xlsx)의 구성품 값을 업데이트

    Parameters:
    - source_file: 구성품이 있는 기준 파일 (contentslist.xlsx)
    - target_file: 구성품을 채워야 할 대상 파일 (2.xlsx)
    - result_file: 결과 저장 파일 경로
    - header_row: 엑셀에서 헤더가 있는 행 번호 (0부터 시작, 보통 1이면 두 번째 줄)
    """
    # 읽기
    source_df = pd.read_excel(source_file, sheet_name=source_sheet, header=header_row, engine='openpyxl')
    target_df = pd.read_excel(target_file, sheet_name=target_sheet, header=header_row, engine='openpyxl')

    # 문자열 처리 및 공백 제거
    source_df[source_qaid_col] = source_df[source_qaid_col].astype(str).str.strip()
    source_df[source_component_col] = source_df[source_component_col].astype(str).str.strip()
    target_df[target_qaid_col] = target_df[target_qaid_col].astype(str).str.strip()

    # 병합을 위한 구성품 딕셔너리
    component_map = dict(zip(source_df[source_qaid_col], source_df[source_component_col]))

    # 구성품 채우기
    target_df[target_component_col] = target_df[target_qaid_col].map(component_map)

    # 저장
    target_df.to_excel(result_file, index=False)
    print(f"✅ 구성품 입력 완료! 결과 파일 저장됨: {result_file}")
    print(f"📌 처리된 QAID 수: {target_df[target_component_col].notna().sum()}개")

update_component_by_qaid(
    source_file="contentslist.xlsx",
    target_file="1.xlsx",
    result_file="contentslist_result.xlsx",
    source_sheet='Sheet1',
    target_sheet='Sheet1',
    source_qaid_col='QAID',
    source_component_col='구성품',
    target_qaid_col='QAID',
    target_component_col='구성품',
    header_row=1  # 엑셀에서 두 번째 줄이 열 제목이면 1
 )
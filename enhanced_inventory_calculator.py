import pandas as pd
import numpy as np


def enhanced_inventory_calculator(file_path, result_file,
                                 sheet_name=None, header_row=0):
    """
    향상된 재고 사용량 계산기
    - 당월입고량과 이월재고를 정확히 반영한 주차별 사용량 계산
    - 재고 흐름을 순차적으로 추적하여 정확한 사용량 도출
    
    계산 로직:
    1. 기초재고 = 이월재고 + 당월입고
    2. 각 주차별로 순차적 재고 소진 추적
    3. 실제 사용량 = 이전 주차 잔여재고 - 현재 주차 재고
    4. 재고 부족 시 경고 표시
    """
    print("📂 파일 읽기...")
    
    # 파일 및 시트 읽기
    try:
        excel_file = pd.ExcelFile(file_path)
        if sheet_name is None or sheet_name not in excel_file.sheet_names:
            sheet_name = excel_file.sheet_names[0]
            print(f"  - 첫 번째 시트 '{sheet_name}' 사용")
        
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        print(f"  - 데이터 읽기 완료: {len(df):,}행")
        
    except Exception as e:
        print(f"❌ 파일 읽기 실패: {e}")
        return
    
    # 컬럼 자동 찾기
    print(f"\n🔍 컬럼 찾기...")
    
    def find_column(keywords, columns):
        for col in columns:
            col_str = str(col).strip().lower()
            if any(keyword in col_str for keyword in keywords):
                return col
        return None
    
    # 필요한 컬럼 찾기
    columns = {
        '이월재고': find_column(['이월재고', '이월', '기초재고'], df.columns),
        '당월입고': find_column(['당월입고', '입고', '입고량'], df.columns),
        '1주': find_column(['1주'], df.columns),
        '2주': find_column(['2주'], df.columns),
        '3주': find_column(['3주'], df.columns),
        '4주': find_column(['4주'], df.columns)
    }
    
    # 컬럼 확인
    missing = [k for k, v in columns.items() if v is None]
    if missing:
        print(f"❌ 다음 컬럼을 찾을 수 없습니다: {missing}")
        print(f"📋 사용 가능한 컬럼: {list(df.columns)}")
        return
    
    print(f"✅ 모든 필요 컬럼 발견:")
    for name, col in columns.items():
        print(f"  - {name}: '{col}'")
    
    # 데이터 전처리
    print(f"\n🔧 데이터 전처리...")
    
    # 숫자형 변환 및 결측값 0으로 처리
    for col in columns.values():
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
    
    # 음수값 확인 및 경고
    for name, col in columns.items():
        negative_count = (df[col] < 0).sum()
        if negative_count > 0:
            print(f"  ⚠️ {name} 컬럼에 음수값 {negative_count}개 발견 - 0으로 처리")
            df[col] = df[col].clip(lower=0)
    
    # 계산 수행
    print(f"\n📊 향상된 재고 흐름 계산...")

    # 기본 컬럼 설정 - 기초재고 = 이월재고 + 당월입고
    df['기초재고'] = df[columns['이월재고']] + df[columns['당월입고']]

    # 기초재고 계산 확인
    print(f"  - 기초재고 계산: 이월재고 + 당월입고")
    print(f"  - 이월재고 합계: {df[columns['이월재고']].sum():,.0f}")
    print(f"  - 당월입고 합계: {df[columns['당월입고']].sum():,.0f}")
    print(f"  - 기초재고 합계: {df['기초재고'].sum():,.0f}")
    
    # 결과 컬럼 초기화
    df['1주_사용량'] = 0
    df['2주_사용량'] = 0
    df['3주_사용량'] = 0
    df['4주_사용량'] = 0
    df['총_사용량'] = 0
    df['최종_잔여재고'] = 0
    df['재고_상태'] = 'OK'
    df['계산_상세'] = ''
    df['유효_주차'] = ''
    
    # 주차별 컬럼 정보
    week_info = [
        ('1주', columns['1주'], '1주_사용량'),
        ('2주', columns['2주'], '2주_사용량'),
        ('3주', columns['3주'], '3주_사용량'),
        ('4주', columns['4주'], '4주_사용량')
    ]
    
    print(f"  - 행별 재고 흐름 추적 중...")
    
    # 통계용 변수
    normal_count = 0
    shortage_count = 0
    no_usage_count = 0
    
    for idx, row in df.iterrows():
        # 기초재고 설정 (이월재고 + 당월입고)
        base_stock = row[columns['이월재고']] + row[columns['당월입고']]
        current_stock = base_stock
        calculation_steps = []
        valid_weeks = []
        total_usage = 0
        status = 'OK'

        # 기초재고 확인 (디버깅용)
        if abs(row['기초재고'] - base_stock) > 0.01:
            print(f"  ⚠️ 행 {idx}: 기초재고 불일치 (계산값: {base_stock}, 저장값: {row['기초재고']})")
            df.loc[idx, '기초재고'] = base_stock  # 정정
        
        # 각 주차별 순차 계산
        previous_stock = current_stock  # 이전 주차 재고 추적용

        for week_name, week_col, usage_col in week_info:
            week_stock = row[week_col]

            # 해당 주차에 재고 데이터가 있는 경우만 계산
            if week_stock > 0:
                valid_weeks.append(week_name)

                # 사용량 계산: 이전 재고 - 현재 주차 재고
                if previous_stock >= week_stock:
                    usage = previous_stock - week_stock

                    calculation_steps.append(f"{week_name}: {previous_stock}→{week_stock} (사용량:{usage})")

                    # 다음 주차를 위해 재고 업데이트
                    previous_stock = week_stock
                    current_stock = week_stock
                else:
                    # 재고 부족 상황
                    usage = previous_stock  # 남은 재고 모두 사용
                    status = '재고부족'

                    calculation_steps.append(f"{week_name}: 재고부족 (필요:{week_stock}, 가용재고:{previous_stock}, 사용량:{usage})")

                    # 재고 부족으로 0이 됨
                    previous_stock = 0
                    current_stock = 0

                # 사용량 저장
                df.loc[idx, usage_col] = usage
                total_usage += usage

            elif week_stock == 0:
                # 해당 주차 데이터 없음 (0값) - 재고는 그대로 유지
                calculation_steps.append(f"{week_name}: 데이터없음(재고유지:{previous_stock})")
        
        # 최종 잔여재고 정확한 계산
        # 방법 1: 기초재고 - 총사용량
        calculated_remaining = base_stock - total_usage

        # 방법 2: 마지막 유효 주차의 재고 (current_stock)
        last_week_remaining = current_stock

        # 두 방법이 일치하는지 확인
        if abs(calculated_remaining - last_week_remaining) > 0.01:
            # 불일치 시 기초재고 - 총사용량을 우선 사용
            final_remaining = max(0, calculated_remaining)
            calculation_steps.append(f"잔여재고보정: {last_week_remaining}→{final_remaining}")
        else:
            final_remaining = last_week_remaining

        # 결과 저장
        df.loc[idx, '총_사용량'] = total_usage
        df.loc[idx, '최종_잔여재고'] = final_remaining
        df.loc[idx, '재고_상태'] = status
        df.loc[idx, '계산_상세'] = ' | '.join(calculation_steps)
        df.loc[idx, '유효_주차'] = ', '.join(valid_weeks)

        # 계산 검증 추가
        balance_check = base_stock - (total_usage + final_remaining)
        if abs(balance_check) > 0.01:
            df.loc[idx, '계산_상세'] += f" | 검증오류: 기초재고({base_stock}) ≠ 사용량({total_usage})+잔여({final_remaining})"
        
        # 통계 업데이트
        if status == 'OK':
            normal_count += 1
        elif status == '재고부족':
            shortage_count += 1
        
        if total_usage == 0:
            no_usage_count += 1
    
    # 기초재고 재확인 및 정정
    print(f"  - 기초재고 최종 확인 중...")
    df['기초재고'] = df[columns['이월재고']] + df[columns['당월입고']]

    # 사용률 계산 (기초재고 기준)
    df['사용률'] = np.where(df['기초재고'] > 0,
                          (df['총_사용량'] / df['기초재고']) * 100, 0)

    # 재고 회전율 계산 (총 사용량 / 기초재고)
    df['재고_회전율'] = np.where(df['기초재고'] > 0,
                              df['총_사용량'] / df['기초재고'], 0)

    # 평균 재고는 참고용으로 계산 (사용하지 않음)
    df['평균_재고'] = (df['기초재고'] + df['최종_잔여재고']) / 2

    # 최종 검증: 기초재고 = 총사용량 + 최종잔여재고
    df['계산_검증'] = abs(df['기초재고'] - (df['총_사용량'] + df['최종_잔여재고']))
    verification_errors_final = (df['계산_검증'] > 0.01).sum()

    if verification_errors_final > 0:
        print(f"  ⚠️ 최종 계산 검증에서 {verification_errors_final}개 오류 발견")
        # 오류가 있는 행들의 최종_잔여재고 재계산
        error_mask = df['계산_검증'] > 0.01
        df.loc[error_mask, '최종_잔여재고'] = df.loc[error_mask, '기초재고'] - df.loc[error_mask, '총_사용량']
        df.loc[error_mask, '최종_잔여재고'] = df.loc[error_mask, '최종_잔여재고'].clip(lower=0)

        # 계산_상세에 수정 내역 추가
        for idx in df[error_mask].index:
            df.loc[idx, '계산_상세'] += " | 최종잔여재고수정"

        print(f"  ✅ 최종_잔여재고 자동 수정 완료")
    else:
        print(f"  ✅ 최종 계산 검증 통과")

    # 계산 검증
    print(f"  - 계산 검증 중...")
    verification_errors = 0
    for idx, row in df.iterrows():
        expected_base = row[columns['이월재고']] + row[columns['당월입고']]
        if abs(row['기초재고'] - expected_base) > 0.01:
            verification_errors += 1

    if verification_errors > 0:
        print(f"  ⚠️ 기초재고 계산 오류 {verification_errors}개 발견 및 수정")
    else:
        print(f"  ✅ 기초재고 계산 검증 완료")
    
    # 결과 정리
    print(f"\n📈 계산 완료!")
    print(f"  - 전체 처리 행수: {len(df):,}개")
    print(f"  - 정상 계산: {normal_count:,}개")
    print(f"  - 재고 부족: {shortage_count:,}개")
    print(f"  - 사용량 없음: {no_usage_count:,}개")
    
    # 재고 상태별 통계
    status_counts = df['재고_상태'].value_counts()
    print(f"\n📊 재고 상태별 통계:")
    for status, count in status_counts.items():
        print(f"  - {status}: {count:,}개")
    
    # 전체 통계 (기초재고 기준)
    valid_data = df[df['기초재고'] > 0]
    print(f"\n📊 전체 재고 통계 (기초재고 = 이월재고 + 당월입고):")
    print(f"  - 이월재고 합계: {df[columns['이월재고']].sum():,.0f}")
    print(f"  - 당월입고 합계: {df[columns['당월입고']].sum():,.0f}")
    print(f"  - 기초재고 합계: {df['기초재고'].sum():,.0f}")
    print(f"  - 총 사용량 합계: {df['총_사용량'].sum():,.0f}")
    print(f"  - 최종 잔여재고 합계: {df['최종_잔여재고'].sum():,.0f}")
    print(f"  - 평균 사용률: {valid_data['사용률'].mean():.1f}%")
    print(f"  - 평균 재고 회전율: {valid_data['재고_회전율'].mean():.2f}")

    # 기초재고 계산 검증 출력
    total_base_calculated = df[columns['이월재고']].sum() + df[columns['당월입고']].sum()
    total_base_stored = df['기초재고'].sum()
    print(f"\n🔍 기초재고 계산 검증:")
    print(f"  - 계산값 (이월+입고): {total_base_calculated:,.0f}")
    print(f"  - 저장값 (기초재고): {total_base_stored:,.0f}")
    print(f"  - 차이: {abs(total_base_calculated - total_base_stored):,.0f}")

    if abs(total_base_calculated - total_base_stored) < 1:
        print(f"  ✅ 기초재고 계산 정확함")
    else:
        print(f"  ⚠️ 기초재고 계산에 차이가 있습니다")
    
    # 주차별 사용량 통계
    print(f"\n📊 주차별 사용량 통계:")
    for week_name, _, usage_col in week_info:
        total_usage = df[usage_col].sum()
        avg_usage = df[df[usage_col] > 0][usage_col].mean()
        count = (df[usage_col] > 0).sum()
        print(f"  - {week_name}: 총 {total_usage:,.0f}, 평균 {avg_usage:.1f}, 사용 건수 {count:,}개")
    
    # 결과 컬럼 정리
    result_columns = [
        # 원본 데이터
        columns['이월재고'], columns['당월입고'],
        columns['1주'], columns['2주'], columns['3주'], columns['4주'],
        # 계산 결과
        '기초재고', '1주_사용량', '2주_사용량', '3주_사용량', '4주_사용량',
        '총_사용량', '사용률', '최종_잔여재고', '재고_회전율',
        '재고_상태', '유효_주차', '계산_상세', '계산_검증'
    ]
    
    # 다른 컬럼들도 포함
    other_columns = [col for col in df.columns if col not in result_columns]
    final_columns = other_columns + result_columns
    
    result_df = df[final_columns].copy()
    
    # 요약 통계 생성
    summary_stats = pd.DataFrame({
        '항목': ['이월재고', '당월입고', '기초재고', '1주_사용량', '2주_사용량', 
                '3주_사용량', '4주_사용량', '총_사용량', '최종_잔여재고', 
                '사용률(%)', '재고_회전율'],
        '합계': [
            df[columns['이월재고']].sum(),
            df[columns['당월입고']].sum(),
            df['기초재고'].sum(),
            df['1주_사용량'].sum(),
            df['2주_사용량'].sum(),
            df['3주_사용량'].sum(),
            df['4주_사용량'].sum(),
            df['총_사용량'].sum(),
            df['최종_잔여재고'].sum(),
            valid_data['사용률'].mean(),
            valid_data['재고_회전율'].mean()
        ],
        '평균': [
            df[columns['이월재고']].mean(),
            df[columns['당월입고']].mean(),
            df['기초재고'].mean(),
            df['1주_사용량'].mean(),
            df['2주_사용량'].mean(),
            df['3주_사용량'].mean(),
            df['4주_사용량'].mean(),
            df['총_사용량'].mean(),
            df['최종_잔여재고'].mean(),
            valid_data['사용률'].mean(),
            valid_data['재고_회전율'].mean()
        ]
    })
    
    # 재고 부족 항목 분석
    shortage_items = df[df['재고_상태'] == '재고부족'].copy()

    # 계산식 설명 시트 생성
    formula_explanation = pd.DataFrame({
        '계산항목': [
            '기초재고',
            '1주_사용량',
            '2주_사용량',
            '3주_사용량',
            '4주_사용량',
            '총_사용량',
            '사용률',
            '최종_잔여재고',
            '재고_회전율',
            '평균_재고',
            '계산_검증'
        ],
        '계산식': [
            '이월재고 + 당월입고',
            '기초재고 - 1주재고 (단, 1주재고 > 0인 경우만)',
            '1주재고 - 2주재고 (단, 2주재고 > 0인 경우만)',
            '2주재고 - 3주재고 (단, 3주재고 > 0인 경우만)',
            '3주재고 - 4주재고 (단, 4주재고 > 0인 경우만)',
            '1주_사용량 + 2주_사용량 + 3주_사용량 + 4주_사용량',
            '(총_사용량 / 기초재고) × 100',
            '기초재고 - 총_사용량 (또는 마지막 유효 주차의 재고)',
            '총_사용량 / 기초재고',
            '(기초재고 + 최종_잔여재고) / 2',
            'ABS(기초재고 - (총_사용량 + 최종_잔여재고))'
        ],
        '설명': [
            '월초 사용 가능한 총 재고량',
            '첫 번째 주차에 사용된 재고량',
            '두 번째 주차에 사용된 재고량',
            '세 번째 주차에 사용된 재고량',
            '네 번째 주차에 사용된 재고량',
            '전체 기간 동안 사용된 총 재고량',
            '기초재고 대비 사용된 비율 (%)',
            '전체 사용 후 남은 재고량',
            '기초재고 대비 사용된 비율 (사용률과 동일)',
            '기간 중 평균 보유 재고량',
            '계산 정확성 검증 (0에 가까울수록 정확)'
        ],
        '조건': [
            '항상 계산',
            '1주재고 데이터가 0보다 큰 경우만',
            '2주재고 데이터가 0보다 큰 경우만',
            '3주재고 데이터가 0보다 큰 경우만',
            '4주재고 데이터가 0보다 큰 경우만',
            '항상 계산',
            '기초재고 > 0인 경우만',
            '항상 계산 (음수 방지)',
            '기초재고 > 0인 경우만',
            '항상 계산',
            '항상 계산'
        ],
        '예시': [
            '100 + 200 = 300',
            '300 - 250 = 50',
            '250 - 180 = 70',
            '180 - 120 = 60',
            '120 - 80 = 40',
            '50 + 70 + 60 + 40 = 220',
            '(220 / 300) × 100 = 73.3%',
            '300 - 220 = 80',
            '220 / 300 = 0.73',
            '(300 + 80) / 2 = 190',
            'ABS(300 - (220 + 80)) = 0'
        ]
    })

    # 계산 로직 상세 설명
    calculation_logic = pd.DataFrame({
        '단계': [
            '1단계',
            '2단계',
            '3단계',
            '4단계',
            '5단계',
            '6단계',
            '7단계',
            '8단계'
        ],
        '작업내용': [
            '기초재고 계산',
            '유효 주차 확인',
            '순차적 사용량 계산',
            '재고 부족 상황 처리',
            '총 사용량 집계',
            '최종 잔여재고 결정',
            '사용률 및 재고회전율 계산',
            '계산 검증 및 오류 수정'
        ],
        '상세설명': [
            '이월재고와 당월입고를 합산하여 월초 사용 가능한 총 재고량을 계산',
            '각 주차별 재고 데이터 중 0보다 큰 값만 유효한 데이터로 인식',
            '유효한 주차 순서대로 이전 재고에서 현재 재고를 빼서 사용량 계산',
            '필요한 재고보다 가용 재고가 적을 경우 재고부족으로 분류하고 가용 재고 모두 사용',
            '모든 주차별 사용량을 합산하여 총 사용량 계산',
            '기초재고에서 총 사용량을 뺀 값과 마지막 유효 주차 재고 중 정확한 값 선택',
            '기초재고 대비 사용률과 기초재고 대비 재고회전율 계산',
            '기초재고 = 총사용량 + 최종잔여재고 공식으로 검증하고 오류 시 자동 수정'
        ],
        '주의사항': [
            '이월재고와 당월입고가 모두 0이면 계산 불가',
            '모든 주차 재고가 0이면 사용량 계산 불가',
            '음수 사용량 발생 시 0으로 처리',
            '재고 부족 시에도 계산 계속 진행',
            '사용량이 없어도 0으로 기록',
            '두 방법의 결과가 다르면 기초재고-총사용량 우선',
            '기초재고가 0이면 사용률과 재고회전율 모두 0으로 처리',
            '검증 오류 발생 시 자동으로 최종잔여재고 재계산'
        ]
    })

    # 재고 상태 분류 기준
    status_criteria = pd.DataFrame({
        '재고상태': [
            'OK',
            '재고부족'
        ],
        '판정기준': [
            '모든 주차에서 필요한 재고량을 충족',
            '하나 이상의 주차에서 필요한 재고량 부족'
        ],
        '처리방식': [
            '정상적으로 각 주차별 사용량 계산',
            '부족한 주차까지는 가용 재고 모두 사용, 이후 주차는 계산 불가'
        ],
        '예시상황': [
            '기초재고 300, 1주 250, 2주 180 → 모든 주차 계산 가능',
            '기초재고 100, 1주 80, 2주 150 → 2주차에서 재고 부족 (80 < 150)'
        ]
    })

    # 데이터 품질 기준
    data_quality = pd.DataFrame({
        '데이터상태': [
            '정상데이터',
            '부분데이터',
            '무효데이터'
        ],
        '조건': [
            '기초재고 > 0 이고 최소 1개 주차 재고 > 0',
            '기초재고 > 0 이지만 일부 주차만 재고 데이터 존재',
            '기초재고 = 0 또는 모든 주차 재고 = 0'
        ],
        '계산방식': [
            '모든 유효 주차에 대해 순차적 사용량 계산',
            '데이터가 있는 주차만 계산, 없는 주차는 재고 유지',
            '계산 불가, 모든 사용량 0으로 처리'
        ],
        '결과해석': [
            '신뢰할 수 있는 사용량 및 사용률 데이터',
            '부분적으로 신뢰할 수 있는 데이터, 해석 시 주의 필요',
            '분석 불가능한 데이터, 원본 데이터 확인 필요'
        ]
    })

    # 엑셀 저장
    print(f"\n💾 결과 저장 중...")
    
    with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
        # 메인 결과
        result_df.to_excel(writer, sheet_name='향상된계산결과', index=False)
        
        # 요약 통계
        summary_stats.to_excel(writer, sheet_name='요약통계', index=False)
        
        # 재고 상태별 분류
        status_summary = df.groupby('재고_상태').agg({
            '기초재고': ['count', 'sum', 'mean'],
            '총_사용량': ['sum', 'mean'],
            '사용률': 'mean'
        }).round(2)
        status_summary.to_excel(writer, sheet_name='재고상태별분석')
        
        # 재고 부족 항목 (있는 경우)
        if len(shortage_items) > 0:
            shortage_items.to_excel(writer, sheet_name='재고부족항목', index=False)
        
        # 주차별 상세 분석
        weekly_analysis = pd.DataFrame({
            '주차': ['1주', '2주', '3주', '4주'],
            '사용건수': [
                (df['1주_사용량'] > 0).sum(),
                (df['2주_사용량'] > 0).sum(),
                (df['3주_사용량'] > 0).sum(),
                (df['4주_사용량'] > 0).sum()
            ],
            '총사용량': [
                df['1주_사용량'].sum(),
                df['2주_사용량'].sum(),
                df['3주_사용량'].sum(),
                df['4주_사용량'].sum()
            ],
            '평균사용량': [
                df[df['1주_사용량'] > 0]['1주_사용량'].mean(),
                df[df['2주_사용량'] > 0]['2주_사용량'].mean(),
                df[df['3주_사용량'] > 0]['3주_사용량'].mean(),
                df[df['4주_사용량'] > 0]['4주_사용량'].mean()
            ]
        })
        weekly_analysis.to_excel(writer, sheet_name='주차별분석', index=False)

        # 계산식 설명 시트들
        formula_explanation.to_excel(writer, sheet_name='계산식설명', index=False)
        calculation_logic.to_excel(writer, sheet_name='계산로직', index=False)
        status_criteria.to_excel(writer, sheet_name='재고상태기준', index=False)
        data_quality.to_excel(writer, sheet_name='데이터품질기준', index=False)

        # 계산 검증 오류 항목 (있는 경우)
        verification_errors = df[df['계산_검증'] > 0.01]
        if len(verification_errors) > 0:
            verification_errors[['기초재고', '총_사용량', '최종_잔여재고', '계산_검증', '계산_상세']].to_excel(
                writer, sheet_name='계산검증오류', index=False)

        # 원본 데이터
        df_original = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        df_original.to_excel(writer, sheet_name='원본데이터', index=False)
    
    print(f"✅ 저장 완료: {result_file}")
    print(f"📋 저장된 시트:")
    print(f"  - '향상된계산결과': 모든 계산 결과 ({len(result_df):,}행)")
    print(f"  - '요약통계': 항목별 합계/평균")
    print(f"  - '재고상태별분석': 재고 상태별 상세 분석")
    if len(shortage_items) > 0:
        print(f"  - '재고부족항목': 재고 부족 발생 항목 ({len(shortage_items):,}개)")
    print(f"  - '주차별분석': 주차별 사용량 상세 분석")
    print(f"  - '계산식설명': 모든 계산식과 예시")
    print(f"  - '계산로직': 단계별 계산 로직 설명")
    print(f"  - '재고상태기준': 재고 상태 판정 기준")
    print(f"  - '데이터품질기준': 데이터 품질 분류 기준")
    verification_errors = df[df['계산_검증'] > 0.01]
    if len(verification_errors) > 0:
        print(f"  - '계산검증오류': 계산 검증 오류 항목 ({len(verification_errors):,}개)")
    print(f"  - '원본데이터': 원본 데이터")
    
    # 샘플 결과 출력
    print(f"\n📋 계산 결과 샘플 (상위 3개):")
    
    for i, (idx, row) in enumerate(result_df.head(3).iterrows()):
        # 기초재고 계산 확인
        calculated_base = row[columns['이월재고']] + row[columns['당월입고']]
        stored_base = row['기초재고']

        print(f"\n  {i+1}번째 항목:")
        print(f"    이월재고: {row[columns['이월재고']]:,.0f}")
        print(f"    당월입고: {row[columns['당월입고']]:,.0f}")
        print(f"    기초재고: {stored_base:,.0f} (계산값: {calculated_base:,.0f})")

        if abs(calculated_base - stored_base) > 0.01:
            print(f"    ⚠️ 기초재고 불일치!")
        else:
            print(f"    ✅ 기초재고 계산 정확")

        print(f"    주차별 사용량: 1주={row['1주_사용량']:,.0f}, 2주={row['2주_사용량']:,.0f}, 3주={row['3주_사용량']:,.0f}, 4주={row['4주_사용량']:,.0f}")
        print(f"    총 사용량: {row['총_사용량']:,.0f}")
        print(f"    사용률: {row['사용률']:.1f}% (총사용량/기초재고)")
        print(f"    최종 잔여재고: {row['최종_잔여재고']:,.0f}")
        print(f"    재고 회전율: {row['재고_회전율']:.2f}")
        print(f"    재고 상태: {row['재고_상태']}")
        print(f"    유효 주차: {row['유효_주차']}")
    
    return result_df


# 함수 실행
if __name__ == "__main__":
    enhanced_inventory_calculator(
        file_path="inventory.xlsx",  # 실제 파일명으로 변경
        result_file="enhanced_inventory_result.xlsx",  # 결과 파일
        sheet_name=None,  # 자동 탐지
        header_row=0  # 헤더 행 번호
    )

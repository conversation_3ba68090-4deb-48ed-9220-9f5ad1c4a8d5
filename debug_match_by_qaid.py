import pandas as pd
import re


def debug_match_by_qaid(file_a, file_b, result_file,
                       sheet_a='Sheet1', sheet_b='Sheet1',
                       qaid_col_a='구성품', qaid_col_b='구성품',
                       header_row=1):
    """
    디버깅 기능이 강화된 매칭 함수
    A파일과 B파일의 구성품을 다양한 방식으로 매칭하여 결과 도출
    """
    print("📂 파일 읽기 중...")
    
    # 파일 읽기
    df_a = pd.read_excel(file_a, sheet_name=sheet_a, header=header_row)
    df_b = pd.read_excel(file_b, sheet_name=sheet_b, header=header_row)
    
    print(f"  - {file_a}: {len(df_a):,}행 읽기 완료")
    print(f"  - {file_b}: {len(df_b):,}행 읽기 완료")

    # 컬럼 존재 여부 확인
    print(f"\n📋 컬럼 정보:")
    print(f"  - 파일 A 컬럼: {list(df_a.columns)}")
    print(f"  - 파일 B 컬럼: {list(df_b.columns)}")
    
    if qaid_col_a not in df_a.columns:
        raise KeyError(f"파일 A에 '{qaid_col_a}' 컬럼이 없습니다.")
    if qaid_col_b not in df_b.columns:
        raise KeyError(f"파일 B에 '{qaid_col_b}' 컬럼이 없습니다.")

    # 구성품 컬럼 전처리
    df_a[qaid_col_a] = df_a[qaid_col_a].fillna('').astype(str).str.strip()
    df_b[qaid_col_b] = df_b[qaid_col_b].fillna('').astype(str).str.strip()
    
    # 빈 문자열 제거
    df_a_clean = df_a[df_a[qaid_col_a] != ''].copy()
    df_b_clean = df_b[df_b[qaid_col_b] != ''].copy()
    
    print(f"\n🔍 데이터 전처리 결과:")
    print(f"  - 파일 A 유효한 값: {len(df_a_clean):,}개")
    print(f"  - 파일 B 유효한 값: {len(df_b_clean):,}개")

    # 데이터 샘플 출력
    print(f"\n📋 파일 A 구성품 샘플 (상위 10개):")
    for i, value in enumerate(df_a_clean[qaid_col_a].head(10)):
        print(f"  {i+1}. '{value}' (길이: {len(value)})")
    
    print(f"\n📋 파일 B 구성품 샘플 (상위 10개):")
    for i, value in enumerate(df_b_clean[qaid_col_b].head(10)):
        print(f"  {i+1}. '{value}' (길이: {len(value)})")

    # 매칭 결과를 저장할 리스트
    matching_results = []
    
    print(f"\n🔍 매칭 분석 시작...")
    
    # 매칭 방법들을 정의
    def try_exact_match(a_val, b_val):
        """정확히 일치하는지 확인"""
        return a_val.lower().strip() == b_val.lower().strip()
    
    def try_contains_match(a_val, b_val):
        """A가 B에 포함되어 있는지 확인"""
        a_clean = a_val.lower().strip()
        b_clean = b_val.lower().strip()
        return a_clean in b_clean or b_clean in a_clean
    
    def try_word_match(a_val, b_val):
        """단어 기반 매칭"""
        a_words = set(re.findall(r'[가-힣a-zA-Z0-9]+', a_val.lower()))
        b_words = set(re.findall(r'[가-힣a-zA-Z0-9]+', b_val.lower()))
        
        # 1글자 단어 제외
        a_words = {w for w in a_words if len(w) > 1}
        b_words = {w for w in b_words if len(w) > 1}
        
        if not a_words or not b_words:
            return False, 0
        
        # 공통 단어 비율 계산
        common_words = a_words & b_words
        similarity = len(common_words) / min(len(a_words), len(b_words)) if min(len(a_words), len(b_words)) > 0 else 0
        
        return similarity > 0.3, similarity  # 30% 이상 유사하면 매칭
    
    def try_fuzzy_match(a_val, b_val):
        """유사도 기반 매칭"""
        a_clean = re.sub(r'[^가-힣a-zA-Z0-9]', '', a_val.lower())
        b_clean = re.sub(r'[^가-힣a-zA-Z0-9]', '', b_val.lower())
        
        if not a_clean or not b_clean:
            return False, 0
        
        # 간단한 유사도 계산 (공통 문자 비율)
        common_chars = sum(1 for c in a_clean if c in b_clean)
        similarity = common_chars / max(len(a_clean), len(b_clean))
        
        return similarity > 0.5, similarity  # 50% 이상 유사하면 매칭

    # A 파일의 각 행에 대해 B 파일에서 매칭 시도
    total_comparisons = len(df_a_clean) * len(df_b_clean)
    print(f"  - 총 비교 횟수: {total_comparisons:,}회")
    
    comparison_count = 0
    
    for idx_a, row_a in df_a_clean.iterrows():
        a_component = row_a[qaid_col_a]
        
        for idx_b, row_b in df_b_clean.iterrows():
            b_component = row_b[qaid_col_b]
            comparison_count += 1
            
            # 진행률 출력 (1000회마다)
            if comparison_count % 1000 == 0:
                progress = (comparison_count / total_comparisons) * 100
                print(f"    진행률: {progress:.1f}% ({comparison_count:,}/{total_comparisons:,})")
            
            # 다양한 매칭 방법 시도
            match_type = None
            similarity_score = 0
            
            # 방법 1: 정확 일치
            if try_exact_match(a_component, b_component):
                match_type = "정확일치"
                similarity_score = 1.0
            
            # 방법 2: 포함 관계
            elif try_contains_match(a_component, b_component):
                match_type = "포함관계"
                similarity_score = 0.8
            
            # 방법 3: 단어 기반 매칭
            else:
                is_word_match, word_similarity = try_word_match(a_component, b_component)
                if is_word_match:
                    match_type = "단어매칭"
                    similarity_score = word_similarity
                
                # 방법 4: 유사도 기반 매칭
                else:
                    is_fuzzy_match, fuzzy_similarity = try_fuzzy_match(a_component, b_component)
                    if is_fuzzy_match:
                        match_type = "유사도매칭"
                        similarity_score = fuzzy_similarity
            
            # 매칭된 경우 결과에 추가
            if match_type:
                result_row = row_a.to_dict()
                result_row[f'B파일_{qaid_col_b}'] = b_component
                result_row['매칭여부'] = '매칭됨'
                result_row['매칭방식'] = match_type
                result_row['유사도점수'] = round(similarity_score, 3)
                result_row['A파일_원본구성품'] = a_component
                result_row['B파일_원본구성품'] = b_component
                
                matching_results.append(result_row)

    print(f"\n📊 매칭 결과:")
    print(f"  - 총 매칭된 결과: {len(matching_results):,}개")
    
    if len(matching_results) == 0:
        print("\n❌ 매칭된 결과가 없습니다. 추가 분석을 진행합니다...")
        
        # 매칭이 안 된 경우 상세 분석
        print(f"\n🔍 상세 분석:")
        
        # A와 B의 구성품 길이 분포 확인
        a_lengths = df_a_clean[qaid_col_a].str.len()
        b_lengths = df_b_clean[qaid_col_b].str.len()
        
        print(f"  - A 구성품 길이: 평균 {a_lengths.mean():.1f}, 최소 {a_lengths.min()}, 최대 {a_lengths.max()}")
        print(f"  - B 구성품 길이: 평균 {b_lengths.mean():.1f}, 최소 {b_lengths.min()}, 최대 {b_lengths.max()}")
        
        # 특수문자 포함 여부 확인
        a_special = df_a_clean[qaid_col_a].str.contains(r'[^가-힣a-zA-Z0-9\s]').sum()
        b_special = df_b_clean[qaid_col_b].str.contains(r'[^가-힣a-zA-Z0-9\s]').sum()
        
        print(f"  - A 구성품 특수문자 포함: {a_special}개")
        print(f"  - B 구성품 특수문자 포함: {b_special}개")
        
        # 간단한 결과 파일 생성
        analysis_df = pd.DataFrame({
            '분석항목': ['A파일 행수', 'B파일 행수', '매칭 결과수', 'A 평균길이', 'B 평균길이'],
            '값': [len(df_a_clean), len(df_b_clean), len(matching_results), 
                  round(a_lengths.mean(), 1), round(b_lengths.mean(), 1)]
        })
        
        with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
            analysis_df.to_excel(writer, sheet_name='분석결과', index=False)
            df_a_clean.head(20).to_excel(writer, sheet_name='A파일샘플', index=False)
            df_b_clean.head(20).to_excel(writer, sheet_name='B파일샘플', index=False)
        
        print(f"\n📄 분석 결과 파일 저장: {result_file}")
        
    else:
        # 매칭 결과가 있는 경우
        matching_df = pd.DataFrame(matching_results)
        
        # 매칭 방식별 통계
        match_stats = matching_df['매칭방식'].value_counts()
        print(f"\n📊 매칭 방식별 통계:")
        for method, count in match_stats.items():
            print(f"  - {method}: {count:,}개")
        
        # 결과 저장
        with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
            matching_df.to_excel(writer, sheet_name='매칭결과', index=False)
            
            # 매칭 통계
            stats_df = pd.DataFrame({
                '매칭방식': match_stats.index,
                '개수': match_stats.values
            })
            stats_df.to_excel(writer, sheet_name='매칭통계', index=False)
        
        print(f"\n✅ 결과 파일 저장 완료: {result_file}")
        
        # 매칭 결과 샘플 출력
        print(f"\n📋 매칭 결과 샘플:")
        for i, result in enumerate(matching_results[:5]):
            print(f"  {i+1}. A: '{result['A파일_원본구성품'][:30]}...'")
            print(f"     B: '{result['B파일_원본구성품'][:30]}...'")
            print(f"     방식: {result['매칭방식']}, 점수: {result['유사도점수']}")


# 함수 실행
if __name__ == "__main__":
    debug_match_by_qaid(
        file_a="A.xlsx",
        file_b="B.xlsx",
        result_file="debug_matched_result.xlsx",
        sheet_a="Sheet1",
        sheet_b="Sheet1",
        qaid_col_a="구성품",
        qaid_col_b="구성품",
        header_row=1
    )

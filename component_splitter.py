import pandas as pd
import numpy as np


def split_components_by_comma(file_path, result_file, sheet_name='Sheet1', header_row=0):
    """
    a.xlsx 파일의 "구성품" 컬럼에서 ","가 포함된 경우 ","의 수를 기준으로 항목을 복사하여 결과파일을 만드는 함수
    
    Parameters:
    - file_path: 분석할 엑셀 파일 경로 (a.xlsx)
    - result_file: 결과 저장 파일 경로
    - sheet_name: 시트명 (기본값: 'Sheet1')
    - header_row: 헤더 행 번호 (0부터 시작)
    """
    print("📂 파일 읽기 및 구성품 분할 처리 시작...")
    
    # 파일 및 시트 읽기
    try:
        excel_file = pd.ExcelFile(file_path)
        available_sheets = excel_file.sheet_names
        print(f"  - 사용 가능한 시트: {available_sheets}")
        
        if sheet_name not in available_sheets:
            print(f"  ⚠️ '{sheet_name}' 시트를 찾을 수 없습니다. 첫 번째 시트를 사용합니다.")
            sheet_name = available_sheets[0]
        
        print(f"  - 사용할 시트: '{sheet_name}'")
        
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        print(f"  - 데이터 읽기 완료: {len(df):,}행 × {len(df.columns):,}열")
        
        # 구성품 컬럼 확인
        if '구성품' not in df.columns:
            print(f"❌ '구성품' 컬럼을 찾을 수 없습니다.")
            print(f"📋 사용 가능한 컬럼: {list(df.columns)}")
            return
        
        print(f"✅ '구성품' 컬럼 발견")
        
    except Exception as e:
        print(f"❌ 파일 읽기 실패: {e}")
        return
    
    # 데이터 전처리
    print(f"\n🔧 데이터 전처리...")
    
    # 구성품 컬럼을 문자열로 변환하고 결측값 처리
    df['구성품'] = df['구성품'].astype(str).fillna('')
    
    # 빈 문자열과 'nan' 제거
    df_clean = df[~df['구성품'].isin(['', 'nan', 'NaN'])].copy()
    
    print(f"  - 전처리 후 데이터: {len(df_clean):,}행")
    
    # 쉼표 분석
    print(f"\n🔍 쉼표 분석 중...")
    
    # 각 행의 쉼표 개수 계산
    df_clean['쉼표개수'] = df_clean['구성품'].str.count(',')
    df_clean['분할개수'] = df_clean['쉼표개수'] + 1  # 쉼표 개수 + 1 = 분할될 항목 수
    
    # 쉼표 통계
    comma_stats = df_clean['쉼표개수'].value_counts().sort_index()
    print(f"  - 쉼표 개수별 통계:")
    for comma_count, row_count in comma_stats.items():
        print(f"    • {comma_count}개 쉼표: {row_count:,}행 (분할 후 {comma_count + 1}개 항목)")
    
    total_items_after_split = df_clean['분할개수'].sum()
    print(f"  - 분할 전 총 행수: {len(df_clean):,}행")
    print(f"  - 분할 후 예상 행수: {total_items_after_split:,}행")
    
    # 구성품 분할 및 행 복사
    print(f"\n📊 구성품 분할 및 행 복사 중...")
    
    result_rows = []
    
    for idx, row in df_clean.iterrows():
        if idx % 1000 == 0 and idx > 0:
            print(f"  - 진행률: {idx:,}/{len(df_clean):,} ({(idx/len(df_clean)*100):.1f}%)")
        
        components = row['구성품']
        comma_count = row['쉼표개수']
        
        if comma_count == 0:
            # 쉼표가 없는 경우 원본 그대로
            new_row = row.copy()
            new_row['원본행번호'] = idx + 1
            new_row['분할순번'] = 1
            new_row['분할된구성품'] = components.strip()
            new_row['원본구성품'] = components
            result_rows.append(new_row)
        else:
            # 쉼표가 있는 경우 분할
            split_components = [comp.strip() for comp in components.split(',')]
            
            for split_idx, component in enumerate(split_components):
                if component:  # 빈 문자열이 아닌 경우만
                    new_row = row.copy()
                    new_row['원본행번호'] = idx + 1
                    new_row['분할순번'] = split_idx + 1
                    new_row['분할된구성품'] = component
                    new_row['원본구성품'] = components
                    result_rows.append(new_row)
    
    # 결과 데이터프레임 생성
    result_df = pd.DataFrame(result_rows)
    
    # 컬럼 순서 재정렬 (새로 추가된 컬럼을 앞쪽에 배치)
    new_columns = ['원본행번호', '분할순번', '분할된구성품', '원본구성품']
    original_columns = [col for col in df.columns if col not in new_columns]
    final_columns = new_columns + original_columns
    
    result_df = result_df[final_columns]
    
    print(f"  - 분할 완료!")
    print(f"  - 최종 결과 행수: {len(result_df):,}행")
    
    # 분할 결과 분석
    print(f"\n📈 분할 결과 분석...")
    
    # 분할 통계
    split_stats = result_df.groupby('원본행번호').agg({
        '분할순번': 'count',
        '원본구성품': 'first'
    }).rename(columns={'분할순번': '분할된개수'})
    
    split_distribution = split_stats['분할된개수'].value_counts().sort_index()
    print(f"  - 분할 개수별 통계:")
    for split_count, row_count in split_distribution.items():
        print(f"    • {split_count}개로 분할: {row_count:,}개 원본행")
    
    # 가장 많이 분할된 항목들
    top_split = split_stats.nlargest(5, '분할된개수')
    print(f"\n🔝 가장 많이 분할된 항목 TOP 5:")
    for idx, (orig_row, row_data) in enumerate(top_split.iterrows()):
        print(f"  {idx+1}. 원본행 {orig_row}: {row_data['분할된개수']}개로 분할")
        print(f"     원본: '{row_data['원본구성품'][:100]}{'...' if len(row_data['원본구성품']) > 100 else ''}'")
    
    # 요약 통계 생성
    summary_stats = pd.DataFrame([
        ['원본 파일명', file_path],
        ['원본 시트명', sheet_name],
        ['처리 일시', pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')],
        ['', ''],
        ['원본 전체 행수', len(df)],
        ['처리 대상 행수', len(df_clean)],
        ['분할 후 총 행수', len(result_df)],
        ['증가 행수', len(result_df) - len(df_clean)],
        ['증가율', f"{((len(result_df) - len(df_clean)) / len(df_clean) * 100):.1f}%"],
        ['', ''],
        ['쉼표 없는 행수', len(df_clean[df_clean['쉼표개수'] == 0])],
        ['쉼표 있는 행수', len(df_clean[df_clean['쉼표개수'] > 0])],
        ['최대 쉼표 개수', df_clean['쉼표개수'].max()],
        ['평균 쉼표 개수', round(df_clean['쉼표개수'].mean(), 2)],
        ['', ''],
        ['최대 분할 개수', split_stats['분할된개수'].max()],
        ['평균 분할 개수', round(split_stats['분할된개수'].mean(), 2)]
    ], columns=['항목', '값'])
    
    # 쉼표 개수별 상세 분석
    comma_detail = df_clean.groupby('쉼표개수').agg({
        '구성품': 'count',
        '분할개수': 'sum'
    }).rename(columns={'구성품': '원본행수', '분할개수': '분할후행수'})
    comma_detail['증가행수'] = comma_detail['분할후행수'] - comma_detail['원본행수']
    comma_detail = comma_detail.reset_index()
    
    # 엑셀 저장
    print(f"\n💾 결과 저장 중...")
    
    with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
        # 1. 메인 결과 - 분할된 데이터
        result_df.to_excel(writer, sheet_name='분할결과', index=False)
        
        # 2. 요약 통계
        summary_stats.to_excel(writer, sheet_name='요약통계', index=False)
        
        # 3. 쉼표 개수별 상세 분석
        comma_detail.to_excel(writer, sheet_name='쉼표개수별분석', index=False)
        
        # 4. 분할 통계 (원본행 기준)
        split_stats_export = split_stats.reset_index()
        split_stats_export.to_excel(writer, sheet_name='분할통계', index=False)
        
        # 5. 분할 개수별 분포
        split_distribution_df = pd.DataFrame({
            '분할개수': split_distribution.index,
            '원본행수': split_distribution.values,
            '비율': round((split_distribution.values / len(split_stats)) * 100, 2)
        })
        split_distribution_df.to_excel(writer, sheet_name='분할개수별분포', index=False)
        
        # 6. 원본 데이터
        df.to_excel(writer, sheet_name='원본데이터', index=False)
    
    print(f"✅ 저장 완료: {result_file}")
    print(f"📋 저장된 시트:")
    print(f"  - '분할결과': 쉼표로 분할된 최종 결과 ({len(result_df):,}행)")
    print(f"  - '요약통계': 전체 분할 처리 통계")
    print(f"  - '쉼표개수별분석': 쉼표 개수별 상세 분석")
    print(f"  - '분할통계': 원본행별 분할 통계")
    print(f"  - '분할개수별분포': 분할 개수별 분포")
    print(f"  - '원본데이터': 원본 데이터")
    
    # 샘플 결과 출력
    print(f"\n📋 분할 결과 샘플 (상위 10개):")
    sample_df = result_df.head(10)
    for i, (_, row) in enumerate(sample_df.iterrows()):
        print(f"  {i+1:2d}. 원본행{row['원본행번호']:3d}-{row['분할순번']}번째: '{row['분할된구성품']}'")
        if i == 0:  # 첫 번째 항목만 원본도 표시
            print(f"      (원본: '{row['원본구성품']}')")
    
    # 분할 효과 요약
    print(f"\n📊 분할 처리 효과:")
    print(f"  - 원본 행수: {len(df_clean):,}행")
    print(f"  - 분할 후 행수: {len(result_df):,}행")
    print(f"  - 증가 행수: {len(result_df) - len(df_clean):,}행")
    print(f"  - 증가율: {((len(result_df) - len(df_clean)) / len(df_clean) * 100):.1f}%")
    
    return result_df


# 함수 실행
if __name__ == "__main__":
    split_components_by_comma(
        file_path="a.xlsx",  # 분석할 파일
        result_file="component_split_result.xlsx",  # 결과 파일
        sheet_name="Sheet1",  # 시트명
        header_row=0  # 헤더 행 번호
    )

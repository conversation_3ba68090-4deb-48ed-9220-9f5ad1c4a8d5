import pandas as pd


def find_matching_words(file_a, file_b, result_file,
                       sheet_a='Sheet1', sheet_b='Sheet1',
                       col_a='구성품', col_b='구성품',
                       header_row=1):
    """
    A.xlsx 파일의 "구성품" 컬럼에서 B.xlsx의 "구성품" 컬럼의 단어가 포함된 행을 찾아서
    상품명, bar_cd, 매칭된 단어와 함께 결과 파일로 저장

    Parameters:
    - file_a: 기준이 되는 파일 경로 (A.xlsx)
    - file_b: 비교할 대상 파일 경로 (B.xlsx)
    - result_file: 결과 저장 파일 경로
    - sheet_a, sheet_b: 각 파일의 시트명
    - col_a, col_b: 각 파일의 비교할 컬럼명
    - header_row: 엑셀 헤더 행 번호 (0부터 시작)
    """
    print("📂 파일 읽기 중...")

    # 파일 읽기
    df_a = pd.read_excel(file_a, sheet_name=sheet_a, header=header_row)
    df_b = pd.read_excel(file_b, sheet_name=sheet_b, header=header_row)

    print(f"  - {file_a}: {len(df_a):,}행 읽기 완료")
    print(f"  - {file_b}: {len(df_b):,}행 읽기 완료")

    # 컬럼 존재 여부 확인
    if col_a not in df_a.columns:
        raise KeyError(f"파일 A에 '{col_a}' 컬럼이 없습니다. 사용 가능한 컬럼: {list(df_a.columns)}")
    if col_b not in df_b.columns:
        raise KeyError(f"파일 B에 '{col_b}' 컬럼이 없습니다. 사용 가능한 컬럼: {list(df_b.columns)}")

    # A 파일에 필요한 컬럼 확인
    required_cols_a = ['상품명', 'bar_cd']
    missing_cols_a = [col for col in required_cols_a if col not in df_a.columns]
    if missing_cols_a:
        print(f"⚠️ 파일 A에 다음 컬럼이 없습니다: {missing_cols_a}")
        print(f"사용 가능한 컬럼: {list(df_a.columns)}")

    print(f"\n🔍 단어 매칭 분석 중...")
    print(f"  - 파일 A 컬럼: '{col_a}'")
    print(f"  - 파일 B 컬럼: '{col_b}'")

    # 데이터 전처리 (NaN 값 처리 및 문자열 변환)
    df_a[col_a] = df_a[col_a].fillna('').astype(str).str.strip()
    df_b[col_b] = df_b[col_b].fillna('').astype(str).str.strip()

    # 빈 문자열 제거
    df_a_clean = df_a[df_a[col_a] != ''].copy()
    df_b_clean = df_b[df_b[col_b] != ''].copy()

    print(f"  - 파일 A 유효한 값: {len(df_a_clean):,}개")
    print(f"  - 파일 B 유효한 값: {len(df_b_clean):,}개")

    # B 파일의 고유 구성품 목록 추출
    b_words = set(df_b_clean[col_b].unique())

    print(f"  - 파일 A 고유값: {len(set(df_a_clean[col_a].unique())):,}개")
    print(f"  - 파일 B 고유값: {len(b_words):,}개")

    # 매칭 결과를 저장할 리스트
    matching_results = []

    print(f"\n🔍 단어 매칭 진행 중...")

    # A 파일의 각 행에 대해 B 파일의 단어가 포함되어 있는지 확인
    for idx, row in df_a_clean.iterrows():
        a_component = row[col_a]
        a_product_name = row.get('상품명', 'N/A')
        a_bar_cd = row.get('bar_cd', 'N/A')

        # B 파일의 각 단어가 A의 구성품에 포함되어 있는지 확인
        matched_words = []
        for b_word in b_words:
            if b_word and b_word.lower() in a_component.lower():
                matched_words.append(b_word)

        # 매칭된 단어가 있으면 결과에 추가
        if matched_words:
            for matched_word in matched_words:
                matching_results.append({
                    'A파일_구성품': a_component,
                    '상품명': a_product_name,
                    'bar_cd': a_bar_cd,
                    'B파일_매칭단어': matched_word,
                    '매칭여부': '매칭됨'
                })

    print(f"  - 매칭된 결과: {len(matching_results):,}개")

    # 매칭 통계 계산
    total_a_rows = len(df_a_clean)
    matched_a_rows = len(set([result['A파일_구성품'] for result in matching_results]))
    unmatched_a_rows = total_a_rows - matched_a_rows

    print(f"  - 전체 A 파일 행: {total_a_rows:,}개")
    print(f"  - 매칭된 A 파일 행: {matched_a_rows:,}개")
    print(f"  - 매칭되지 않은 A 파일 행: {unmatched_a_rows:,}개")
    
    # 결과 데이터프레임 생성 및 저장
    if len(matching_results) > 0:
        # 매칭 결과 데이터프레임 생성
        matching_df = pd.DataFrame(matching_results)

        # 매칭되지 않은 A 파일 행들 추출
        matched_components = set([result['A파일_구성품'] for result in matching_results])
        unmatched_rows = df_a_clean[~df_a_clean[col_a].isin(matched_components)].copy()
        unmatched_rows['매칭여부'] = '매칭안됨'
        unmatched_rows['B파일_매칭단어'] = 'N/A'

        # 매칭 통계 생성
        stats_df = pd.DataFrame({
            '구분': ['전체 A 파일 행수', '매칭된 A 파일 행수', '매칭되지 않은 A 파일 행수',
                    '총 매칭 결과수', 'B 파일 고유 단어수'],
            '개수': [total_a_rows, matched_a_rows, unmatched_a_rows,
                    len(matching_results), len(b_words)]
        })

        # 엑셀 파일로 저장 (여러 시트)
        with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
            # 시트 1: 매칭된 결과
            matching_df.to_excel(writer, sheet_name='매칭결과', index=False)

            # 시트 2: 매칭되지 않은 행들
            if len(unmatched_rows) > 0:
                unmatched_display = unmatched_rows[['구성품', '상품명', 'bar_cd', '매칭여부', 'B파일_매칭단어']].copy() if '상품명' in unmatched_rows.columns and 'bar_cd' in unmatched_rows.columns else unmatched_rows[[col_a, '매칭여부', 'B파일_매칭단어']].copy()
                unmatched_display.to_excel(writer, sheet_name='매칭안된결과', index=False)

            # 시트 3: 통계 정보
            stats_df.to_excel(writer, sheet_name='통계정보', index=False)

            # 시트 4: B 파일 단어 목록
            b_words_df = pd.DataFrame({
                'B파일_구성품목록': sorted(list(b_words))
            })
            b_words_df.to_excel(writer, sheet_name='B파일단어목록', index=False)

        print(f"\n✅ 결과 파일 저장 완료: {result_file}")
        print(f"📋 저장된 시트:")
        print(f"  - '매칭결과': 매칭된 결과 ({len(matching_results):,}개)")
        if len(unmatched_rows) > 0:
            print(f"  - '매칭안된결과': 매칭되지 않은 A 파일 행 ({len(unmatched_rows):,}개)")
        print(f"  - '통계정보': 매칭 통계")
        print(f"  - 'B파일단어목록': B 파일의 모든 구성품 ({len(b_words):,}개)")

        # 매칭 결과 샘플 출력
        print(f"\n📋 매칭 결과 샘플:")
        for i, result in enumerate(matching_results[:5]):
            print(f"  {i+1}. A구성품: '{result['A파일_구성품'][:50]}...' → B매칭단어: '{result['B파일_매칭단어']}'")
        if len(matching_results) > 5:
            print(f"  ... 외 {len(matching_results) - 5}개")

    else:
        # 매칭된 결과가 없는 경우
        result_df = pd.DataFrame({
            '결과': ['A 파일의 구성품에서 B 파일의 단어가 포함된 항목이 없습니다.'],
            '파일A_총행수': [total_a_rows],
            '파일B_고유단어수': [len(b_words)],
            '매칭결과수': [0]
        })
        result_df.to_excel(result_file, index=False)

        print(f"\n❌ 결과: 매칭된 항목이 없습니다!")
        print(f"📋 A 파일의 구성품에서 B 파일의 단어가 포함된 항목을 찾을 수 없습니다.")
        print(f"📄 결과 파일 저장: {result_file}")

    print(f"\n📈 최종 분석 요약:")
    print(f"  - 전체 A 파일 행수: {total_a_rows:,}개")
    print(f"  - B 파일 고유 단어수: {len(b_words):,}개")
    print(f"  - 매칭된 결과수: {len(matching_results):,}개")
    print(f"  - 매칭된 A 파일 행수: {matched_a_rows:,}개")
    print(f"  - 매칭되지 않은 A 파일 행수: {unmatched_a_rows:,}개")


# 함수 실행
if __name__ == "__main__":
    find_matching_words(
        file_a="A.xlsx",  # 기준 파일
        file_b="B.xlsx",  # 비교 대상 파일
        result_file="matching_words_result.xlsx",  # 결과 파일
        sheet_a="Sheet1",
        sheet_b="Sheet1",
        col_a="구성품",
        col_b="구성품",
        header_row=1  # 엑셀에서 두 번째 행이 헤더인 경우
    )

import pandas as pd


def find_missing_values(file_a, file_b, result_file,
                       sheet_a='Sheet1', sheet_b='Sheet1',
                       col_a='구성품', col_b='구성품',
                       header_row=1):
    """
    파일 A의 컬럼에는 있지만 파일 B의 컬럼에는 없는 값들을 찾아서 결과 파일로 저장

    Parameters:
    - file_a: 기준이 되는 파일 경로 (A.xlsx)
    - file_b: 비교할 대상 파일 경로 (B.xlsx)
    - result_file: 결과 저장 파일 경로
    - sheet_a, sheet_b: 각 파일의 시트명
    - col_a, col_b: 각 파일의 비교할 컬럼명
    - header_row: 엑셀 헤더 행 번호 (0부터 시작)
    """
    print("📂 파일 읽기 중...")
    
    # 파일 읽기
    df_a = pd.read_excel(file_a, sheet_name=sheet_a, header=header_row)
    df_b = pd.read_excel(file_b, sheet_name=sheet_b, header=header_row)
    
    print(f"  - {file_a}: {len(df_a):,}행 읽기 완료")
    print(f"  - {file_b}: {len(df_b):,}행 읽기 완료")

    # 컬럼 존재 여부 확인
    if col_a not in df_a.columns:
        raise KeyError(f"파일 A에 '{col_a}' 컬럼이 없습니다. 사용 가능한 컬럼: {list(df_a.columns)}")
    if col_b not in df_b.columns:
        raise KeyError(f"파일 B에 '{col_b}' 컬럼이 없습니다. 사용 가능한 컬럼: {list(df_b.columns)}")

    print(f"\n🔍 컬럼 비교 분석 중...")
    print(f"  - 파일 A 컬럼: '{col_a}'")
    print(f"  - 파일 B 컬럼: '{col_b}'")

    # 데이터 전처리 (NaN 값 처리 및 문자열 변환)
    df_a[col_a] = df_a[col_a].fillna('').astype(str).str.strip()
    df_b[col_b] = df_b[col_b].fillna('').astype(str).str.strip()
    
    # 빈 문자열 제거
    df_a_clean = df_a[df_a[col_a] != ''].copy()
    df_b_clean = df_b[df_b[col_b] != ''].copy()
    
    print(f"  - 파일 A 유효한 값: {len(df_a_clean):,}개")
    print(f"  - 파일 B 유효한 값: {len(df_b_clean):,}개")

    # 고유값 추출
    values_a = set(df_a_clean[col_a].unique())
    values_b = set(df_b_clean[col_b].unique())
    
    print(f"  - 파일 A 고유값: {len(values_a):,}개")
    print(f"  - 파일 B 고유값: {len(values_b):,}개")

    # 파일 A에는 있지만 파일 B에는 없는 값들 찾기
    missing_in_b = values_a - values_b
    
    print(f"\n📊 분석 결과:")
    print(f"  - 파일 A에만 있는 값: {len(missing_in_b):,}개")
    
    if len(missing_in_b) > 0:
        print(f"  - 누락된 값 샘플:")
        for i, value in enumerate(sorted(list(missing_in_b))[:10]):
            print(f"    {i+1}. '{value}'")
        if len(missing_in_b) > 10:
            print(f"    ... 외 {len(missing_in_b) - 10}개")

    # 누락된 값들과 관련된 전체 행 데이터 추출
    missing_rows = df_a_clean[df_a_clean[col_a].isin(missing_in_b)].copy()
    
    # 결과 데이터프레임 생성
    if len(missing_in_b) > 0:
        # 누락된 값별 통계 정보 추가
        missing_rows['B파일_존재여부'] = 'B파일에 없음'
        missing_rows['발견횟수'] = missing_rows.groupby(col_a)[col_a].transform('count')
        
        # 고유값별 요약 테이블 생성
        summary_df = pd.DataFrame({
            '누락된_구성품': sorted(list(missing_in_b)),
            'A파일_출현횟수': [missing_rows[missing_rows[col_a] == val].shape[0] for val in sorted(list(missing_in_b))],
            'B파일_존재여부': ['없음'] * len(missing_in_b)
        })
        
        # 엑셀 파일로 저장 (여러 시트)
        with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
            # 시트 1: 누락된 값들의 전체 행 데이터
            missing_rows.to_excel(writer, sheet_name='누락된_행_전체데이터', index=False)
            
            # 시트 2: 누락된 값들의 요약
            summary_df.to_excel(writer, sheet_name='누락된_값_요약', index=False)
            
            # 시트 3: 통계 정보
            stats_df = pd.DataFrame({
                '구분': ['파일 A 총 행수', '파일 B 총 행수', '파일 A 유효값', '파일 B 유효값', 
                        '파일 A 고유값', '파일 B 고유값', 'A에만 있는 값', '공통값'],
                '개수': [len(df_a), len(df_b), len(df_a_clean), len(df_b_clean),
                        len(values_a), len(values_b), len(missing_in_b), len(values_a & values_b)]
            })
            stats_df.to_excel(writer, sheet_name='통계정보', index=False)
        
        print(f"\n✅ 결과 파일 저장 완료: {result_file}")
        print(f"📋 저장된 시트:")
        print(f"  1. '누락된_행_전체데이터': 누락된 값이 포함된 전체 행 ({len(missing_rows):,}행)")
        print(f"  2. '누락된_값_요약': 누락된 고유값 목록 ({len(missing_in_b):,}개)")
        print(f"  3. '통계정보': 전체 분석 통계")
        
    else:
        # 누락된 값이 없는 경우
        result_df = pd.DataFrame({
            '결과': ['파일 A의 모든 구성품이 파일 B에도 존재합니다.'],
            '파일A_고유값수': [len(values_a)],
            '파일B_고유값수': [len(values_b)],
            '공통값수': [len(values_a & values_b)]
        })
        result_df.to_excel(result_file, index=False)
        
        print(f"\n✅ 결과: 누락된 값이 없습니다!")
        print(f"📋 파일 A의 모든 구성품이 파일 B에도 존재합니다.")
        print(f"📄 결과 파일 저장: {result_file}")

    # 추가 분석 정보 출력
    common_values = values_a & values_b
    only_in_b = values_b - values_a
    
    print(f"\n📈 상세 분석:")
    print(f"  - 공통값: {len(common_values):,}개")
    print(f"  - A에만 있는 값: {len(missing_in_b):,}개")
    print(f"  - B에만 있는 값: {len(only_in_b):,}개")
    
    if len(only_in_b) > 0:
        print(f"  - B에만 있는 값 샘플:")
        for i, value in enumerate(sorted(list(only_in_b))[:5]):
            print(f"    {i+1}. '{value}'")
        if len(only_in_b) > 5:
            print(f"    ... 외 {len(only_in_b) - 5}개")


# 함수 실행
if __name__ == "__main__":
    find_missing_values(
        file_a="A.xlsx",  # 기준 파일
        file_b="B.xlsx",  # 비교 대상 파일
        result_file="missing_values_result.xlsx",  # 결과 파일
        sheet_a="Sheet1",
        sheet_b="Sheet1", 
        col_a="구성품",
        col_b="구성품",
        header_row=1  # 엑셀에서 두 번째 행이 헤더인 경우
    )

import pandas as pd
import numpy as np

# 파일 경로 지정
price_file = "price.xlsx"
ref_file = "1.xlsx"

# 엑셀 파일 읽기 (모든 컬럼 가져오기)
price_df = pd.read_excel(
    price_file,
    sheet_name="모니터사이즈(미기재품목)",
    header=1,
    usecols=["제품명", "수리완료", "점검완료"]
)
ref_df = pd.read_excel(ref_file, sheet_name="Sheet1", header=1)

# 컬럼명 정리 (공백 제거)
price_df.columns = price_df.columns.str.strip()
ref_df.columns = ref_df.columns.str.strip()

# 필수 컬럼 확인
required_price_cols = ["제품명", "수리완료", "점검완료"]
required_ref_cols = ["제품명", "수리상태", "수리비용"]
for col in required_price_cols:
    if col not in price_df.columns:
        raise KeyError(f"[price.xlsx] '{col}' 컬럼 누락")
for col in required_ref_cols:
    if col not in ref_df.columns:
        raise KeyError(f"[1.xlsx] '{col}' 컬럼 누락")

# 데이터 정규화
def normalize(df, col):
    if col in df.columns:
        return df[col].astype(str).str.strip().str.lower()
    return np.nan

price_df["제품명"] = normalize(price_df, "제품명")
price_df["수리완료"] = pd.to_numeric(price_df["수리완료"], errors="coerce")
price_df["점검완료"] = pd.to_numeric(price_df["점검완료"], errors="coerce")

ref_df["제품명"] = normalize(ref_df, "제품명")
ref_df["수리상태"] = normalize(ref_df, "수리상태")
ref_df["수리비용"] = pd.to_numeric(ref_df["수리비용"], errors="coerce")

# 검증 결과 컬럼 추가
ref_df["검증결과"] = "미처리"
ref_df["매칭금액"] = np.nan

# 모든 행에 대해 검증 수행
for idx, row in ref_df.iterrows():
    status = row["수리상태"]
    product = row["제품명"]
    cost = row["수리비용"]

    # "XL" 상태는 무조건 "XL"로 처리
    if status == "xl":
        ref_df.at[idx, "검증결과"] = "XL"
        continue

    matched = price_df[price_df["제품명"] == product]

    if matched.empty:
        ref_df.at[idx, "검증결과"] = "기타항목"
        continue

    if status == "리퍼완료":
        price_value = matched.iloc[0]["수리완료"]
        ref_df.at[idx, "매칭금액"] = price_value
        if pd.isna(price_value):
            ref_df.at[idx, "검증결과"] = "오류: 미확인"
        elif pd.notna(cost) and float(price_value) == float(cost):
            ref_df.at[idx, "검증결과"] = "정상"
        else:
            ref_df.at[idx, "검증결과"] = "오류: 금액 불일치"

    elif status == "점검완료(중)":
        price_value = matched.iloc[0]["점검완료"]
        ref_df.at[idx, "매칭금액"] = price_value
        if pd.isna(price_value):
            ref_df.at[idx, "검증결과"] = "오류: 미확인"
        elif pd.notna(cost) and float(price_value) == float(cost):
            ref_df.at[idx, "검증결과"] = "정상"
        else:
            ref_df.at[idx, "검증결과"] = "오류: 금액 불일치"
    else:
        ref_df.at[idx, "검증결과"] = "XL"

# 중복 제거 (모든 컬럼 기준)
ref_df = ref_df.drop_duplicates()

# 결과 저장
result_file = "moitor_result.xlsx"
ref_df.to_excel(result_file, index=False)

print(f"✅ 검증 완료! 저장 경로: {result_file}")
print(f"✅ 총 처리 건수: {len(ref_df):,}건")
print(f"✅ 최종 컬럼: {ref_df.columns.tolist()}")

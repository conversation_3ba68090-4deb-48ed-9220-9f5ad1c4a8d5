import pandas as pd

# 파일 경로 지정
price_file = "contents.xlsx"
ref_file = "1.xlsx"

# 엑셀 파일 읽기
price_df = pd.read_excel(price_file, sheet_name="Sheet1", header=1, usecols=["제품명", "구성품", "별도수리비"])
ref_df = pd.read_excel(ref_file, sheet_name="Sheet1", header=1, usecols=["QAID","제품명", "구성품", "별도수리비"])

# 컬럼명 정리
price_df.columns = price_df.columns.str.strip()
ref_df.columns = ref_df.columns.str.strip()

# 데이터 타입 정리
price_df["제품명"] = price_df["제품명"].astype(str).str.strip()
price_df["구성품"] = price_df["구성품"].astype(str).str.strip()
price_df["별도수리비"] = pd.to_numeric(price_df["별도수리비"], errors='coerce')

ref_df["QAID"] = ref_df["QAID"].astype(str).str.strip()
ref_df["제품명"] = ref_df["제품명"].astype(str).str.strip()
ref_df["구성품"] = ref_df["구성품"].astype(str).str.strip()
ref_df["별도수리비"] = pd.to_numeric(ref_df["별도수리비"], errors='coerce')

# 병합
merged_df = pd.merge(ref_df, price_df, on="제품명", how='left', suffixes=('_ref', '_price'))

# 검증 함수 정의
def check_result(row):
    if not (pd.isna(row['구성품_price']) and pd.isna(row['별도수리비_price'])):
        if row['구성품_ref'] == row['구성품_price'] and row['별도수리비_ref'] == row['별도수리비_price']:
            return "정상 (모두 일치)"
        elif row['구성품_ref'] == row['구성품_price'] and pd.isna(row['별도수리비_ref']) and pd.isna(row['별도수리비_price']):
            return "정상 (구성품 일치, 비용 NaN)"
        elif row['구성품_ref'] == row['구성품_price']:
            return "오류: 구성품 일치, 비용 불일치"
        elif row['별도수리비_ref'] == row['별도수리비_price'] and pd.isna(row['구성품_ref']) and pd.isna(row['구성품_price']):
            return "정상 (비용 일치, 구성품 NaN)"
        elif row['별도수리비_ref'] == row['별도수리비_price']:
            return "오류: 구성품 불일치, 비용 일치"
        else:
            return "오류: 구성품, 비용 모두 불일치"
    else:
        return "구성품 미포함"

# 검증 결과 컬럼 생성
merged_df['검증결과'] = merged_df.apply(check_result, axis=1)

# --------------------------
# 중복 제거 코드 추가 부분!
# --------------------------
# QAID가 유일하다면 QAID 기준으로 중복 제거
# 중복 기준 컬럼을 원하는 대로 지정할 수 있습니다.
merged_df = merged_df.drop_duplicates(subset=['QAID'])

# 만약 전체 행이 완전히 같은 경우만 중복으로 보고 싶으면
# merged_df = merged_df.drop_duplicates()

# 결과 저장
result_file = "contents_result.xlsx"
merged_df.to_excel(result_file, index=False)

print(f"검증 완료! 결과 파일 저장: {result_file}")
print(f"처리된 행 수: {len(merged_df)}개")

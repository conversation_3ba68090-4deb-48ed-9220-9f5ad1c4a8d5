import pandas as pd


def match_by_qaid_enhanced(file_a, file_b, result_file,
                          sheet_a='Sheet1', sheet_b='Sheet1',
                          qaid_col_a='구성품', qaid_col_b='구성품',
                          header_row=1, keywords=None):
    """
    파일 A를 기준으로 파일 B에서 QAID가 일치하는 데이터만 추출하여 새 파일 생성
    A.xlsx의 구성품 단어가 B.xlsx의 구성품에 정확히 모두 포함된 경우에만 매칭

    Parameters:
    - file_a: 기준이 되는 파일 경로 (A.xlsx)
    - file_b: 매칭할 대상 파일 경로 (B.xlsx)
    - result_file: 결과 저장 파일 경로
    - sheet_a, sheet_b: 각 파일의 시트명
    - qaid_col_a, qaid_col_b: 각 파일의 QAID 컬럼명
    - header_row: 엑셀 헤더 행 번호 (0부터 시작)
    - keywords: 구성품에서 검색할 특정 단어 리스트 (None이면 정확히 일치하는 문자열로 매칭)
    """
    print("📂 파일 읽기 중...")
    
    # 파일 읽기
    df_a = pd.read_excel(file_a, sheet_name=sheet_a, header=header_row)
    df_b = pd.read_excel(file_b, sheet_name=sheet_b, header=header_row)
    
    print(f"  - {file_a}: {len(df_a):,}행 읽기 완료")
    print(f"  - {file_b}: {len(df_b):,}행 읽기 완료")

    # 컬럼 존재 여부 확인
    if qaid_col_a not in df_a.columns:
        raise KeyError(f"파일 A에 '{qaid_col_a}' 컬럼이 없습니다. 사용 가능한 컬럼: {list(df_a.columns)}")
    if qaid_col_b not in df_b.columns:
        raise KeyError(f"파일 B에 '{qaid_col_b}' 컬럼이 없습니다. 사용 가능한 컬럼: {list(df_b.columns)}")

    # 구성품 컬럼 전처리 (NaN 값 처리 포함)
    df_a[qaid_col_a] = df_a[qaid_col_a].fillna('').astype(str).str.strip()
    df_b[qaid_col_b] = df_b[qaid_col_b].fillna('').astype(str).str.strip()
    
    # 빈 문자열 제거
    df_a_clean = df_a[df_a[qaid_col_a] != ''].copy()
    df_b_clean = df_b[df_b[qaid_col_b] != ''].copy()
    
    print(f"🔍 단어 완전 포함 매칭 분석 중...")
    print(f"  - 파일 A 유효한 값: {len(df_a_clean):,}개")
    print(f"  - 파일 B 유효한 값: {len(df_b_clean):,}개")

    # 매칭 결과를 저장할 리스트
    matching_results = []
    
    # A 파일의 각 행에 대해 B 파일에서 완전 포함 매칭 확인
    for idx_a, row_a in df_a_clean.iterrows():
        a_component = row_a[qaid_col_a].lower()  # 대소문자 구분 없이 비교
        
        # A의 구성품을 단어로 분리 (공백, 쉼표, 기타 구분자로 분리)
        import re
        a_words = re.findall(r'\b\w+\b', a_component)  # 단어만 추출
        a_words = [word for word in a_words if len(word) > 1]  # 1글자 단어 제외
        
        if not a_words:  # 유효한 단어가 없으면 건너뛰기
            continue
            
        # B 파일에서 A의 모든 단어가 포함된 항목 찾기
        for idx_b, row_b in df_b_clean.iterrows():
            b_component = row_b[qaid_col_b].lower()
            
            # A의 모든 단어가 B에 포함되어 있는지 확인
            all_words_included = all(word in b_component for word in a_words)
            
            if all_words_included:
                # 매칭된 결과 생성 (A의 모든 컬럼 + B의 구성품 컬럼)
                result_row = row_a.to_dict()  # A의 모든 컬럼
                result_row[f'B파일_{qaid_col_b}'] = row_b[qaid_col_b]  # B의 구성품 컬럼
                result_row['매칭여부'] = '매칭됨'
                result_row['A파일_단어수'] = len(a_words)
                result_row['매칭된_A단어'] = ', '.join(a_words)
                
                matching_results.append(result_row)

    print(f"  - 완전 포함 매칭된 결과: {len(matching_results):,}개")
    
    # 매칭 통계 계산
    total_a_rows = len(df_a_clean)
    matched_results_count = len(matching_results)
    unique_matched_a_components = len(set([result[qaid_col_a] for result in matching_results])) if matching_results else 0
    unmatched_a_rows = total_a_rows - unique_matched_a_components
    
    print(f"  - 전체 A 파일 행: {total_a_rows:,}개")
    print(f"  - 매칭된 결과 행: {matched_results_count:,}개")
    print(f"  - 고유 매칭된 A 구성품: {unique_matched_a_components:,}개")
    print(f"  - 매칭되지 않은 A 파일 행: {unmatched_a_rows:,}개")

    # 결과 데이터프레임 생성 및 저장
    if len(matching_results) > 0:
        # 매칭 결과 데이터프레임 생성
        matching_df = pd.DataFrame(matching_results)
        
        # 매칭되지 않은 A 파일 행들 추출
        matched_a_components = set([result[qaid_col_a] for result in matching_results])
        unmatched_rows = df_a_clean[~df_a_clean[qaid_col_a].isin(matched_a_components)].copy()
        unmatched_rows['매칭여부'] = '매칭안됨'
        unmatched_rows[f'B파일_{qaid_col_b}'] = 'N/A'
        unmatched_rows['A파일_단어수'] = 0
        unmatched_rows['매칭된_A단어'] = 'N/A'
        
        # 매칭 통계 생성
        stats_df = pd.DataFrame({
            '구분': ['전체 A 파일 행수', '매칭된 결과 행수', '고유 매칭된 A 구성품수', 
                    '매칭되지 않은 A 파일 행수', 'B 파일 총 행수'],
            '개수': [total_a_rows, matched_results_count, unique_matched_a_components, 
                    unmatched_a_rows, len(df_b_clean)]
        })
        
        # 엑셀 파일로 저장 (여러 시트)
        with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
            # 시트 1: 매칭된 결과 (A의 모든 컬럼 + B의 구성품)
            matching_df.to_excel(writer, sheet_name='완전포함매칭결과', index=False)
            
            # 시트 2: 매칭되지 않은 A 파일 행들
            if len(unmatched_rows) > 0:
                unmatched_rows.to_excel(writer, sheet_name='매칭안된A파일행', index=False)
            
            # 시트 3: 통계 정보
            stats_df.to_excel(writer, sheet_name='매칭통계', index=False)
            
            # 시트 4: B 파일 전체 데이터
            df_b_clean.to_excel(writer, sheet_name='B파일전체데이터', index=False)

        print(f"\n✅ 결과 파일 저장 완료: {result_file}")
        print(f"📋 저장된 시트:")
        print(f"  - '완전포함매칭결과': A의 모든 컬럼 + B의 구성품 ({len(matching_results):,}개)")
        if len(unmatched_rows) > 0:
            print(f"  - '매칭안된A파일행': 매칭되지 않은 A 파일 행 ({len(unmatched_rows):,}개)")
        print(f"  - '매칭통계': 매칭 통계 정보")
        print(f"  - 'B파일전체데이터': B 파일의 모든 데이터 ({len(df_b_clean):,}개)")
        
        # 매칭 결과 샘플 출력
        print(f"\n📋 매칭 결과 샘플:")
        for i, result in enumerate(matching_results[:5]):
            a_comp = result[qaid_col_a][:30] + '...' if len(result[qaid_col_a]) > 30 else result[qaid_col_a]
            b_comp = result[f'B파일_{qaid_col_b}'][:30] + '...' if len(result[f'B파일_{qaid_col_b}']) > 30 else result[f'B파일_{qaid_col_b}']
            print(f"  {i+1}. A구성품: '{a_comp}' ↔ B구성품: '{b_comp}'")
            print(f"     매칭된 A단어: {result['매칭된_A단어']}")
        if len(matching_results) > 5:
            print(f"  ... 외 {len(matching_results) - 5}개")
            
    else:
        # 매칭된 결과가 없는 경우
        result_df = pd.DataFrame({
            '결과': ['A 파일의 구성품 단어가 B 파일에 완전히 포함된 항목이 없습니다.'],
            '파일A_총행수': [total_a_rows],
            '파일B_총행수': [len(df_b_clean)],
            '매칭결과수': [0]
        })
        result_df.to_excel(result_file, index=False)
        
        print(f"\n❌ 결과: 완전 포함 매칭된 항목이 없습니다!")
        print(f"📋 A 파일의 구성품 단어가 B 파일에 완전히 포함된 항목을 찾을 수 없습니다.")
        print(f"📄 결과 파일 저장: {result_file}")

    print(f"\n📈 최종 분석 요약:")
    print(f"  - 전체 A 파일 행수: {total_a_rows:,}개")
    print(f"  - 전체 B 파일 행수: {len(df_b_clean):,}개")
    print(f"  - 완전 포함 매칭된 결과 행수: {len(matching_results):,}개")
    print(f"  - 고유 매칭된 A 구성품수: {unique_matched_a_components:,}개")
    print(f"  - 매칭되지 않은 A 파일 행수: {unmatched_a_rows:,}개")


# 함수 실행
if __name__ == "__main__":
    match_by_qaid_enhanced(
        file_a="A.xlsx",  # 기준 파일
        file_b="B.xlsx",  # 매칭할 파일
        result_file="enhanced_matched_result.xlsx",  # 결과 파일
        sheet_a="Sheet1",
        sheet_b="Sheet1",
        qaid_col_a="구성품",
        qaid_col_b="구성품",
        header_row=1,  # 엑셀에서 두 번째 행이 헤더인 경우
        keywords=None  # None으로 설정하면 완전 포함 매칭
    )

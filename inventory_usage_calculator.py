import pandas as pd
import numpy as np


def calculate_weekly_usage(file_path, result_file,
                          sheet_name=None, header_row=0):
    """
    재고량을 기준으로 주차별 사용량을 계산하는 함수

    Parameters:
    - file_path: 엑셀 파일 경로
    - result_file: 결과 저장 파일 경로
    - sheet_name: 시트명 (None이면 자동 탐지)
    - header_row: 헤더 행 번호 (0부터 시작)
    """
    print("📂 파일 읽기 중...")

    # 먼저 사용 가능한 시트 목록 확인
    try:
        excel_file = pd.ExcelFile(file_path)
        available_sheets = excel_file.sheet_names
        print(f"  - 사용 가능한 시트: {available_sheets}")

        # 시트명이 지정되지 않았거나 존재하지 않는 경우
        if sheet_name is None or sheet_name not in available_sheets:
            if sheet_name is not None:
                print(f"  - 지정된 시트 '{sheet_name}'을 찾을 수 없습니다.")

            # 첫 번째 시트를 기본값으로 사용
            sheet_name = available_sheets[0]
            print(f"  - 첫 번째 시트 '{sheet_name}'을 사용합니다.")

        # 파일 읽기
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        print(f"  - {file_path} (시트: {sheet_name}): {len(df):,}행 읽기 완료")

    except Exception as e:
        print(f"  - 파일 읽기 실패: {str(e)}")
        print(f"  - 파일 경로를 확인해주세요: {file_path}")
        raise
    
    # 컬럼 정보 출력
    print(f"\n📋 컬럼 목록 (총 {len(df.columns)}개):")
    for i, col in enumerate(df.columns):
        print(f"  {i+1}. '{col}'")
    
    # 필요한 컬럼 찾기
    def find_columns(df):
        """필요한 컬럼들을 자동으로 찾기"""
        columns_found = {}
        
        # 이월재고 컬럼 찾기
        for col in df.columns:
            col_str = str(col).strip().lower()
            if any(keyword in col_str for keyword in ['이월재고', '이월', '전월재고', '기초재고']):
                columns_found['이월재고'] = col
                break
        
        # 당월입고 컬럼 찾기 (당월입고량 → 당월입고로 변경)
        for col in df.columns:
            col_str = str(col).strip().lower()
            if any(keyword in col_str for keyword in ['당월입고', '입고량', '입고', '당월입고량']):
                columns_found['당월입고'] = col
                break
        
        # 주차별 컬럼 찾기
        week_columns = {}
        for week in ['1주', '2주', '3주', '4주']:
            for col in df.columns:
                col_str = str(col).strip()
                if week in col_str:
                    week_columns[week] = col
                    break
        
        columns_found.update(week_columns)
        
        return columns_found
    
    # 컬럼 찾기
    found_columns = find_columns(df)
    
    print(f"\n🔍 발견된 컬럼:")
    for key, value in found_columns.items():
        if value:
            print(f"  - {key}: '{value}'")
        else:
            print(f"  - {key}: 찾을 수 없음")
    
    # 필수 컬럼 확인
    required_columns = ['이월재고', '당월입고량', '1주', '2주', '3주', '4주']
    missing_columns = [col for col in required_columns if col not in found_columns or not found_columns[col]]
    
    if missing_columns:
        print(f"\n❌ 다음 컬럼을 찾을 수 없습니다: {missing_columns}")
        print("\n💡 해결 방법:")
        print("1. 컬럼명이 정확한지 확인해주세요.")
        print("2. 다음과 같은 컬럼명을 사용해보세요:")
        print("   - 이월재고: '이월재고', '이월', '전월재고', '기초재고'")
        print("   - 당월입고량: '당월입고', '입고량', '입고', '당월입고량'")
        print("   - 주차별: '1주', '2주', '3주', '4주'")
        print("3. 헤더 행 번호가 올바른지 확인해주세요.")

        # 유사한 컬럼명 제안
        print(f"\n🔍 현재 파일의 컬럼 중 유사한 것들:")
        all_columns = [str(col).lower() for col in df.columns]

        suggestions = {
            '이월재고': [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['재고', '이월', '기초'])],
            '당월입고량': [col for col in df.columns if any(keyword in str(col).lower() for keyword in ['입고', '당월', '입고량'])],
            '주차별': [col for col in df.columns if any(keyword in str(col) for keyword in ['주', '1', '2', '3', '4'])]
        }

        for category, suggested_cols in suggestions.items():
            if suggested_cols:
                print(f"  - {category} 관련: {suggested_cols}")

        return
    
    # 데이터 전처리
    print(f"\n🔧 데이터 전처리 중...")
    
    # 숫자형 데이터로 변환
    numeric_columns = ['이월재고', '당월입고량', '1주', '2주', '3주', '4주']
    for col_key in numeric_columns:
        col_name = found_columns[col_key]
        df[col_name] = pd.to_numeric(df[col_name], errors='coerce').fillna(0)
    
    # 계산용 컬럼명 매핑
    이월재고_col = found_columns['이월재고']
    당월입고량_col = found_columns['당월입고량']
    week1_col = found_columns['1주']
    week2_col = found_columns['2주']
    week3_col = found_columns['3주']
    week4_col = found_columns['4주']
    
    # 계산 수행
    print(f"\n📊 사용량 계산 중...")
    
    # 1. 총 가용재고 계산 (이월재고 + 당월입고량)
    df['총_가용재고'] = df[이월재고_col] + df[당월입고량_col]
    
    # 2. 주차별 사용량 계산
    # 1주차 사용량 = 총 가용재고 - 1주차 재고
    df['1주차_사용량'] = df['총_가용재고'] - df[week1_col]
    
    # 2주차 사용량 = 1주차 재고 - 2주차 재고
    df['2주차_사용량'] = df[week1_col] - df[week2_col]
    
    # 3주차 사용량 = 2주차 재고 - 3주차 재고
    df['3주차_사용량'] = df[week2_col] - df[week3_col]
    
    # 4주차 사용량 = 3주차 재고 - 4주차 재고
    df['4주차_사용량'] = df[week3_col] - df[week4_col]
    
    # 3. 총 사용량 계산
    df['총_사용량'] = df['1주차_사용량'] + df['2주차_사용량'] + df['3주차_사용량'] + df['4주차_사용량']
    
    # 4. 잔여재고 계산 (4주차 재고)
    df['잔여재고'] = df[week4_col]
    
    # 5. 사용률 계산 (총 사용량 / 총 가용재고 * 100)
    df['사용률_퍼센트'] = np.where(df['총_가용재고'] > 0, 
                                (df['총_사용량'] / df['총_가용재고']) * 100, 0)
    
    # 6. 주차별 평균 사용량 계산
    df['주차별_평균사용량'] = df['총_사용량'] / 4
    
    # 7. 검증: 계산 검증 (총 가용재고 = 총 사용량 + 잔여재고)
    df['계산_검증'] = np.where(
        abs(df['총_가용재고'] - (df['총_사용량'] + df['잔여재고'])) < 0.01,
        '정상', '오류'
    )
    
    # 결과 정리
    print(f"\n📈 계산 결과 요약:")
    print(f"  - 총 처리 행수: {len(df):,}개")
    print(f"  - 계산 오류 행수: {len(df[df['계산_검증'] == '오류']):,}개")
    print(f"  - 총 가용재고 합계: {df['총_가용재고'].sum():,.0f}")
    print(f"  - 총 사용량 합계: {df['총_사용량'].sum():,.0f}")
    print(f"  - 잔여재고 합계: {df['잔여재고'].sum():,.0f}")
    print(f"  - 전체 평균 사용률: {df['사용률_퍼센트'].mean():.1f}%")
    
    # 주차별 사용량 통계
    print(f"\n📊 주차별 사용량 통계:")
    for week in ['1주차', '2주차', '3주차', '4주차']:
        usage_col = f'{week}_사용량'
        total_usage = df[usage_col].sum()
        avg_usage = df[usage_col].mean()
        print(f"  - {week}: 총 {total_usage:,.0f}, 평균 {avg_usage:.1f}")
    
    # 결과 저장
    print(f"\n💾 결과 저장 중...")
    
    # 요약 통계 생성
    summary_stats = pd.DataFrame({
        '구분': ['총 가용재고', '1주차 사용량', '2주차 사용량', '3주차 사용량', '4주차 사용량', 
                '총 사용량', '잔여재고', '평균 사용률(%)'],
        '합계': [df['총_가용재고'].sum(), df['1주차_사용량'].sum(), df['2주차_사용량'].sum(),
                df['3주차_사용량'].sum(), df['4주차_사용량'].sum(), df['총_사용량'].sum(),
                df['잔여재고'].sum(), df['사용률_퍼센트'].mean()],
        '평균': [df['총_가용재고'].mean(), df['1주차_사용량'].mean(), df['2주차_사용량'].mean(),
                df['3주차_사용량'].mean(), df['4주차_사용량'].mean(), df['총_사용량'].mean(),
                df['잔여재고'].mean(), df['사용률_퍼센트'].mean()],
        '최대값': [df['총_가용재고'].max(), df['1주차_사용량'].max(), df['2주차_사용량'].max(),
                 df['3주차_사용량'].max(), df['4주차_사용량'].max(), df['총_사용량'].max(),
                 df['잔여재고'].max(), df['사용률_퍼센트'].max()],
        '최소값': [df['총_가용재고'].min(), df['1주차_사용량'].min(), df['2주차_사용량'].min(),
                 df['3주차_사용량'].min(), df['4주차_사용량'].min(), df['총_사용량'].min(),
                 df['잔여재고'].min(), df['사용률_퍼센트'].min()]
    })
    
    # 사용률별 분류
    usage_classification = pd.DataFrame({
        '사용률_구간': ['0-20%', '21-40%', '41-60%', '61-80%', '81-100%', '100% 초과'],
        '항목수': [
            len(df[(df['사용률_퍼센트'] >= 0) & (df['사용률_퍼센트'] <= 20)]),
            len(df[(df['사용률_퍼센트'] > 20) & (df['사용률_퍼센트'] <= 40)]),
            len(df[(df['사용률_퍼센트'] > 40) & (df['사용률_퍼센트'] <= 60)]),
            len(df[(df['사용률_퍼센트'] > 60) & (df['사용률_퍼센트'] <= 80)]),
            len(df[(df['사용률_퍼센트'] > 80) & (df['사용률_퍼센트'] <= 100)]),
            len(df[df['사용률_퍼센트'] > 100])
        ]
    })
    
    # 엑셀 파일로 저장
    with pd.ExcelWriter(result_file, engine='openpyxl') as writer:
        # 메인 결과
        df.to_excel(writer, sheet_name='사용량_계산결과', index=False)
        
        # 요약 통계
        summary_stats.to_excel(writer, sheet_name='요약통계', index=False)
        
        # 사용률별 분류
        usage_classification.to_excel(writer, sheet_name='사용률별_분류', index=False)
        
        # 계산 오류 항목 (있는 경우)
        error_rows = df[df['계산_검증'] == '오류']
        if len(error_rows) > 0:
            error_rows.to_excel(writer, sheet_name='계산오류_항목', index=False)
        
        # 원본 데이터
        df_original = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        df_original.to_excel(writer, sheet_name='원본데이터', index=False)
    
    print(f"\n✅ 결과 파일 저장 완료: {result_file}")
    print(f"📋 저장된 시트:")
    print(f"  - '사용량_계산결과': 모든 계산 결과 ({len(df):,}행)")
    print(f"  - '요약통계': 전체 통계 요약")
    print(f"  - '사용률별_분류': 사용률 구간별 분류")
    if len(df[df['계산_검증'] == '오류']) > 0:
        print(f"  - '계산오류_항목': 계산 오류가 있는 항목들")
    print(f"  - '원본데이터': 원본 데이터")
    
    # 샘플 결과 출력
    print(f"\n📋 계산 결과 샘플 (상위 5개):")
    sample_columns = ['총_가용재고', '1주차_사용량', '2주차_사용량', '3주차_사용량', '4주차_사용량', 
                     '총_사용량', '잔여재고', '사용률_퍼센트']
    
    for i, row in df.head(5).iterrows():
        print(f"\n  {i+1}번째 항목:")
        for col in sample_columns:
            if col == '사용률_퍼센트':
                print(f"    {col}: {row[col]:.1f}%")
            else:
                print(f"    {col}: {row[col]:,.0f}")


# 함수 실행
if __name__ == "__main__":
    # 파일명을 실제 파일명으로 변경해주세요
    calculate_weekly_usage(
        file_path="inventory.xlsx",  # 재고 데이터 파일 (실제 파일명으로 변경)
        result_file="weekly_usage_result.xlsx",  # 결과 파일
        sheet_name=None,  # 시트명 (None이면 자동 탐지)
        header_row=0  # 헤더 행 번호 (0부터 시작, 필요시 조정)
    )
